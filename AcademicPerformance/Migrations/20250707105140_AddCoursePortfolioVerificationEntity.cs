﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class AddCoursePortfolioVerificationEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CoursePortfolioVerifications",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianTc = table.Column<string>(type: "character varying(11)", maxLength: 11, nullable: false),
                    CourseCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    PeriodName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CourseName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ExamPapersStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    AnswerKeyStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    ExamRecordStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    AttendanceSheetStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    CourseSyllabusStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    WeeklyAttendanceStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    MakeupExamGradesStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    UzemRecordsStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false, defaultValue: "Pending"),
                    VerificationNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    LastVerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastVerifiedByArchivistId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EbysReferenceId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AcademicianProfileId = table.Column<string>(type: "text", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CoursePortfolioVerifications", x => x.Id);
                    table.CheckConstraint("CK_CoursePortfolioVerification_AnswerKeyStatus", "\"AnswerKeyStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_AttendanceSheetStatus", "\"AttendanceSheetStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_CourseSyllabusStatus", "\"CourseSyllabusStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_ExamPapersStatus", "\"ExamPapersStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_ExamRecordStatus", "\"ExamRecordStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_MakeupExamGradesStatus", "\"MakeupExamGradesStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_UzemRecordsStatus", "\"UzemRecordsStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.CheckConstraint("CK_CoursePortfolioVerification_WeeklyAttendanceStatus", "\"WeeklyAttendanceStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.ForeignKey(
                        name: "FK_CoursePortfolioVerifications_AcademicianProfiles_Academicia~",
                        column: x => x.AcademicianProfileId,
                        principalTable: "AcademicianProfiles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "StaticCriterionCoefficients",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    StaticCriterionSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CriterionName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Coefficient = table.Column<decimal>(type: "numeric(18,4)", nullable: false),
                    MaximumLimit = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    MinimumLimit = table.Column<decimal>(type: "numeric(18,4)", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Category = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DataSource = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastUpdated = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    RowVersion = table.Column<byte[]>(type: "bytea", rowVersion: true, nullable: true),
                    CoefficientHistory = table.Column<string>(type: "text", nullable: true),
                    IsApproved = table.Column<bool>(type: "boolean", nullable: false),
                    ApprovedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaticCriterionCoefficients", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AcademicianProfileId",
                table: "CoursePortfolioVerifications",
                column: "AcademicianProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AcademicianTc",
                table: "CoursePortfolioVerifications",
                column: "AcademicianTc");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AcademicianTc_CourseCode_Perio~",
                table: "CoursePortfolioVerifications",
                columns: new[] { "AcademicianTc", "CourseCode", "PeriodName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_AutoIncrementId",
                table: "CoursePortfolioVerifications",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_CourseCode",
                table: "CoursePortfolioVerifications",
                column: "CourseCode");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_LastVerifiedAt",
                table: "CoursePortfolioVerifications",
                column: "LastVerifiedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_LastVerifiedByArchivistId",
                table: "CoursePortfolioVerifications",
                column: "LastVerifiedByArchivistId");

            migrationBuilder.CreateIndex(
                name: "IX_CoursePortfolioVerifications_PeriodName",
                table: "CoursePortfolioVerifications",
                column: "PeriodName");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CoursePortfolioVerifications");

            migrationBuilder.DropTable(
                name: "StaticCriterionCoefficients");
        }
    }
}
