﻿// <auto-generated />
using System;
using AcademicPerformance.DbContexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    [DbContext(typeof(AcademicPerformanceDbContext))]
    [Migration("20250526105345_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedByControllerUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EvaluationFormAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RejectedByControllerUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("ApprovedByControllerUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationFormAutoIncrementId");

                    b.HasIndex("RejectedByControllerUserId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("AcademicianUniveristyUserId", "EvaluationFormAutoIncrementId")
                        .IsUnique();

                    b.ToTable("AcademicSubmissions", t =>
                        {
                            t.HasCheckConstraint("CK_AcademicSubmission_Status", "\"Status\" IN ('Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'RequiresRevision')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysPermissionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("PermissionName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("PermissionName")
                        .IsUnique();

                    b.ToTable("ApdysPermissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("RoleName")
                        .IsUnique();

                    b.ToTable("ApdysRoles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRolePermissionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<int>("PermissionAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<int>("RoleAutoIncrementId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("PermissionAutoIncrementId");

                    b.HasIndex("RoleAutoIncrementId", "PermissionAutoIncrementId")
                        .IsUnique();

                    b.ToTable("ApdysRolePermissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ChecklistItemDefinitionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("EbysDataSourceHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ItemSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("RequiresEbysVerification")
                        .HasColumnType("boolean");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("Category");

                    b.HasIndex("ItemSystemId")
                        .IsUnique();

                    b.ToTable("ChecklistItemDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicPerformanceDataEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("AssessmentPeriodId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("DataKey")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DataType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DataValue")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubmittedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentPeriodId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("DepartmentId");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("DepartmentId", "AssessmentPeriodId", "DataKey")
                        .IsUnique();

                    b.ToTable("DepartmentStrategicPerformanceData");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("EvaluationPeriodEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EvaluationPeriodStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SubmissionDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationPeriodEndDate");

                    b.HasIndex("EvaluationPeriodStartDate");

                    b.HasIndex("Status");

                    b.ToTable("EvaluationForms", t =>
                        {
                            t.HasCheckConstraint("CK_EvaluationForm_Status", "\"Status\" IN ('Draft', 'Active', 'Closed')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvidenceFileEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AcademicSubmissionAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("FormCriterionLinkId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicSubmissionAutoIncrementId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("UploadedAt");

                    b.ToTable("EvidenceFiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("EvaluationFormAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<decimal?>("WeightPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationFormAutoIncrementId");

                    b.HasIndex("EvaluationFormAutoIncrementId", "DisplayOrder");

                    b.ToTable("FormCategories");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("CriterionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("DynamicCriterionTemplateId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("FormCategoryAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("StaticCriterionSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("WeightPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("FormCategoryAutoIncrementId");

                    b.HasIndex("StaticCriterionSystemId");

                    b.HasIndex("FormCategoryAutoIncrementId", "DisplayOrder");

                    b.ToTable("FormCriterionLinks", t =>
                        {
                            t.HasCheckConstraint("CK_FormCriterionLink_CriterionType", "\"CriterionType\" IN ('Static', 'Dynamic')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryRecordEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("AssessmentPeriodId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("DataKey")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DataType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DataValue")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("EnteredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EnteredByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EntryTypeSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerifiedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EnteredAt");

                    b.HasIndex("EntryTypeSystemId");

                    b.HasIndex("AcademicianUniveristyUserId", "EntryTypeSystemId", "AssessmentPeriodId", "DataKey")
                        .IsUnique();

                    b.ToTable("GenericDataEntryRecords");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioVerificationLogEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EbysReferenceId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ItemSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastVerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastVerifiedByArchivistUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("VerificationStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("ItemSystemId");

                    b.HasIndex("LastVerifiedByArchivistUserId");

                    b.HasIndex("VerificationStatus");

                    b.HasIndex("AcademicianUniveristyUserId", "ItemSystemId")
                        .IsUnique();

                    b.ToTable("PortfolioVerificationLogs", t =>
                        {
                            t.HasCheckConstraint("CK_PortfolioVerification_Status", "\"VerificationStatus\" IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("CompetencyArea")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EvaluationContextId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("FinalScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<decimal?>("PeerAssessmentScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<decimal?>("SelfAssessmentScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubmittedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<decimal?>("SupervisorAssessmentScore")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationContextId");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("AcademicianUniveristyUserId", "EvaluationContextId", "CompetencyArea")
                        .IsUnique();

                    b.ToTable("StaffCompetencyEvaluations");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("CalculationLogic")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("DataSourceHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("SystemId")
                        .IsUnique();

                    b.ToTable("StaticCriterionDefinitions", t =>
                        {
                            t.HasCheckConstraint("CK_StaticCriterion_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.UserApdysRoleMappingEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RevokedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("RoleAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("UniversityUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AssignedAt");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("RoleAutoIncrementId");

                    b.HasIndex("UniversityUserId", "RoleAutoIncrementId")
                        .IsUnique();

                    b.ToTable("UserApdysRoleMappings");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.ToTable("RlxEnums");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EnumId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("WhichRow")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.HasIndex("EnumId");

                    b.ToTable("RlxEnumValues");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxLocalizationDbContextModels.RlxLocalization", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Culture")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ReferenceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.ToTable("RlxLocalizations");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany("Submissions")
                        .HasForeignKey("EvaluationFormAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRolePermissionEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysPermissionEntity", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvidenceFileEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", "AcademicSubmission")
                        .WithMany("EvidenceFiles")
                        .HasForeignKey("AcademicSubmissionAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AcademicSubmission");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany("Categories")
                        .HasForeignKey("EvaluationFormAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", "FormCategory")
                        .WithMany("CriterionLinks")
                        .HasForeignKey("FormCategoryAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", "StaticCriterionDefinition")
                        .WithMany("FormCriterionLinks")
                        .HasForeignKey("StaticCriterionSystemId")
                        .HasPrincipalKey("SystemId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("FormCategory");

                    b.Navigation("StaticCriterionDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioVerificationLogEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ChecklistItemDefinitionEntity", "ChecklistItemDefinition")
                        .WithMany("VerificationLogs")
                        .HasForeignKey("ItemSystemId")
                        .HasPrincipalKey("ItemSystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ChecklistItemDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.UserApdysRoleMappingEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", "Role")
                        .WithMany("UserRoleMappings")
                        .HasForeignKey("RoleAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", "RlxEnum")
                        .WithMany("RlxEnumValues")
                        .HasForeignKey("EnumId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RlxEnum");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.Navigation("EvidenceFiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysPermissionEntity", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", b =>
                {
                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoleMappings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ChecklistItemDefinitionEntity", b =>
                {
                    b.Navigation("VerificationLogs");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", b =>
                {
                    b.Navigation("Categories");

                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.Navigation("CriterionLinks");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", b =>
                {
                    b.Navigation("FormCriterionLinks");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Navigation("RlxEnumValues");
                });
#pragma warning restore 612, 618
        }
    }
}
