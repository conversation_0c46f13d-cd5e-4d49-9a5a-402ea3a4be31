﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class RemoveChecklistItemDefinitionsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_Checklis~",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropTable(
                name: "ChecklistItemDefinitions");

            migrationBuilder.DropIndex(
                name: "IX_PortfolioVerificationLogs_ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropColumn(
                name: "ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "ChecklistItemDefinitions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EbysDataSourceHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ItemSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    RequiresEbysVerification = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChecklistItemDefinitions", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs",
                column: "ChecklistItemDefinitionEntityId");

            migrationBuilder.CreateIndex(
                name: "IX_ChecklistItemDefinitions_AutoIncrementId",
                table: "ChecklistItemDefinitions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChecklistItemDefinitions_Category",
                table: "ChecklistItemDefinitions",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_ChecklistItemDefinitions_ItemSystemId",
                table: "ChecklistItemDefinitions",
                column: "ItemSystemId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_Checklis~",
                table: "PortfolioVerificationLogs",
                column: "ChecklistItemDefinitionEntityId",
                principalTable: "ChecklistItemDefinitions",
                principalColumn: "Id");
        }
    }
}
