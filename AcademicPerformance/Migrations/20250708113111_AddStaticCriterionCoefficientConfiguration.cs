﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class AddStaticCriterionCoefficientConfiguration : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_Category",
                table: "StaticCriterionCoefficients",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_DisplayOrder",
                table: "StaticCriterionCoefficients",
                column: "DisplayOrder");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_EffectiveFrom",
                table: "StaticCriterionCoefficients",
                column: "EffectiveFrom");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_EffectiveTo",
                table: "StaticCriterionCoefficients",
                column: "EffectiveTo");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_IsActive",
                table: "StaticCriterionCoefficients",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_IsApproved",
                table: "StaticCriterionCoefficients",
                column: "IsApproved");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionCoefficients_StaticCriterionSystemId",
                table: "StaticCriterionCoefficients",
                column: "StaticCriterionSystemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_Category",
                table: "StaticCriterionCoefficients");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_DisplayOrder",
                table: "StaticCriterionCoefficients");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_EffectiveFrom",
                table: "StaticCriterionCoefficients");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_EffectiveTo",
                table: "StaticCriterionCoefficients");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_IsActive",
                table: "StaticCriterionCoefficients");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_IsApproved",
                table: "StaticCriterionCoefficients");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionCoefficients_StaticCriterionSystemId",
                table: "StaticCriterionCoefficients");
        }
    }
}
