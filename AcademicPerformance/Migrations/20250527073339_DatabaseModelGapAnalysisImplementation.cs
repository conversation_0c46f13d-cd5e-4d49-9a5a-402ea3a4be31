﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class DatabaseModelGapAnalysisImplementation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_ItemSyst~",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Eval~",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropCheckConstraint(
                name: "CK_EvaluationForm_Status",
                table: "EvaluationForms");

            migrationBuilder.DropIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId_AssessmentP~",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_ChecklistItemDefinitions_ItemSystemId",
                table: "ChecklistItemDefinitions");

            migrationBuilder.DropColumn(
                name: "ApprovedAt",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "Comments",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "CompetencyArea",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "FinalScore",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "PeerAssessmentScore",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "SelfAssessmentScore",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "SubmittedByUserId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "SupervisorAssessmentScore",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "WeightPercentage",
                table: "FormCategories");

            migrationBuilder.DropColumn(
                name: "ApprovedAt",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "ApprovedByUserId",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "DataKey",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "DataType",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "DataValue",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.RenameColumn(
                name: "UploadedByUserId",
                table: "EvidenceFiles",
                newName: "UploadedByUniveristyUserId");

            migrationBuilder.RenameColumn(
                name: "FileSize",
                table: "EvidenceFiles",
                newName: "SizeBytes");

            migrationBuilder.RenameColumn(
                name: "FilePath",
                table: "EvidenceFiles",
                newName: "StoredFilePath");

            migrationBuilder.RenameColumn(
                name: "Comments",
                table: "DepartmentStrategicPerformanceData",
                newName: "TargetValue");

            migrationBuilder.RenameColumn(
                name: "RejectionReason",
                table: "AcademicSubmissions",
                newName: "RejectionComments");

            migrationBuilder.RenameColumn(
                name: "Comments",
                table: "AcademicSubmissions",
                newName: "ApprovalComments");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "StaticCriterionDefinitions",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "EvaluatingManagerUserId",
                table: "StaffCompetencyEvaluations",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "OverallComments",
                table: "StaffCompetencyEvaluations",
                type: "character varying(4000)",
                maxLength: 4000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<double>(
                name: "Weight",
                table: "FormCategories",
                type: "double precision",
                precision: 5,
                scale: 4,
                nullable: false,
                defaultValue: 0.0);

            migrationBuilder.AddColumn<string>(
                name: "SubmittedDynamicDataInstanceId",
                table: "EvidenceFiles",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApplicableAcademicCadresJson",
                table: "EvaluationForms",
                type: "character varying(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SubmittedByUserId",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldMaxLength: 256,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ActualValue",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IndicatorSystemId",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_StaffCompetencyEvaluations_AutoIncrementId",
                table: "StaffCompetencyEvaluations",
                column: "AutoIncrementId");

            migrationBuilder.CreateTable(
                name: "DepartmentStrategicIndicatorDefinitions",
                columns: table => new
                {
                    IndicatorSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DataType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    IsHigherBetter = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DepartmentStrategicIndicatorDefinitions", x => x.IndicatorSystemId);
                    table.CheckConstraint("CK_DepartmentStrategicIndicator_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                });

            migrationBuilder.CreateTable(
                name: "GenericDataEntryDefinitions",
                columns: table => new
                {
                    EntryTypeSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ResponsibleRole = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DataStructureHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GenericDataEntryDefinitions", x => x.EntryTypeSystemId);
                });

            migrationBuilder.CreateTable(
                name: "PortfolioChecklistItemDefinitions",
                columns: table => new
                {
                    ItemSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequiresEbysVerification = table.Column<bool>(type: "boolean", nullable: false),
                    EbysDataSourceHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PortfolioChecklistItemDefinitions", x => x.ItemSystemId);
                });

            migrationBuilder.CreateTable(
                name: "StaffCompetencyDefinitions",
                columns: table => new
                {
                    CompetencySystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(250)", maxLength: 250, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RatingScaleJson = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffCompetencyDefinitions", x => x.CompetencySystemId);
                });

            migrationBuilder.CreateTable(
                name: "CompetencyRatings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    StaffCompetencyEvaluationAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    CompetencySystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Rating = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Comments = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CompetencyRatings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CompetencyRatings_StaffCompetencyDefinitions_CompetencySyst~",
                        column: x => x.CompetencySystemId,
                        principalTable: "StaffCompetencyDefinitions",
                        principalColumn: "CompetencySystemId",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CompetencyRatings_StaffCompetencyEvaluations_StaffCompetenc~",
                        column: x => x.StaffCompetencyEvaluationAutoIncrementId,
                        principalTable: "StaffCompetencyEvaluations",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Eval~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "AcademicianUniveristyUserId", "EvaluationContextId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs",
                column: "ChecklistItemDefinitionEntityId");

            migrationBuilder.AddCheckConstraint(
                name: "CK_EvaluationForm_Status",
                table: "EvaluationForms",
                sql: "\"Status\" IN ('Draft', 'Active', 'Archived')");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId_AssessmentP~",
                table: "DepartmentStrategicPerformanceData",
                columns: new[] { "DepartmentId", "AssessmentPeriodId", "IndicatorSystemId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_IndicatorSystemId",
                table: "DepartmentStrategicPerformanceData",
                column: "IndicatorSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_AutoIncrementId",
                table: "CompetencyRatings",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_CompetencySystemId",
                table: "CompetencyRatings",
                column: "CompetencySystemId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_StaffCompetencyEvaluationAutoIncrementId",
                table: "CompetencyRatings",
                column: "StaffCompetencyEvaluationAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_CompetencyRatings_StaffCompetencyEvaluationAutoIncrementId_~",
                table: "CompetencyRatings",
                columns: new[] { "StaffCompetencyEvaluationAutoIncrementId", "CompetencySystemId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicIndicatorDefinitions_IndicatorSystemId",
                table: "DepartmentStrategicIndicatorDefinitions",
                column: "IndicatorSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicIndicatorDefinitions_IsActive",
                table: "DepartmentStrategicIndicatorDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryDefinitions_EntryTypeSystemId",
                table: "GenericDataEntryDefinitions",
                column: "EntryTypeSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryDefinitions_IsActive",
                table: "GenericDataEntryDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryDefinitions_ResponsibleRole",
                table: "GenericDataEntryDefinitions",
                column: "ResponsibleRole");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioChecklistItemDefinitions_Category",
                table: "PortfolioChecklistItemDefinitions",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioChecklistItemDefinitions_IsActive",
                table: "PortfolioChecklistItemDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioChecklistItemDefinitions_ItemSystemId",
                table: "PortfolioChecklistItemDefinitions",
                column: "ItemSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyDefinitions_CompetencySystemId",
                table: "StaffCompetencyDefinitions",
                column: "CompetencySystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyDefinitions_IsActive",
                table: "StaffCompetencyDefinitions",
                column: "IsActive");

            migrationBuilder.AddForeignKey(
                name: "FK_DepartmentStrategicPerformanceData_DepartmentStrategicIndic~",
                table: "DepartmentStrategicPerformanceData",
                column: "IndicatorSystemId",
                principalTable: "DepartmentStrategicIndicatorDefinitions",
                principalColumn: "IndicatorSystemId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_GenericDataEntryRecords_GenericDataEntryDefinitions_EntryTy~",
                table: "GenericDataEntryRecords",
                column: "EntryTypeSystemId",
                principalTable: "GenericDataEntryDefinitions",
                principalColumn: "EntryTypeSystemId",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_Checklis~",
                table: "PortfolioVerificationLogs",
                column: "ChecklistItemDefinitionEntityId",
                principalTable: "ChecklistItemDefinitions",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_PortfolioVerificationLogs_PortfolioChecklistItemDefinitions~",
                table: "PortfolioVerificationLogs",
                column: "ItemSystemId",
                principalTable: "PortfolioChecklistItemDefinitions",
                principalColumn: "ItemSystemId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DepartmentStrategicPerformanceData_DepartmentStrategicIndic~",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropForeignKey(
                name: "FK_GenericDataEntryRecords_GenericDataEntryDefinitions_EntryTy~",
                table: "GenericDataEntryRecords");

            migrationBuilder.DropForeignKey(
                name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_Checklis~",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropForeignKey(
                name: "FK_PortfolioVerificationLogs_PortfolioChecklistItemDefinitions~",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropTable(
                name: "CompetencyRatings");

            migrationBuilder.DropTable(
                name: "DepartmentStrategicIndicatorDefinitions");

            migrationBuilder.DropTable(
                name: "GenericDataEntryDefinitions");

            migrationBuilder.DropTable(
                name: "PortfolioChecklistItemDefinitions");

            migrationBuilder.DropTable(
                name: "StaffCompetencyDefinitions");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_StaffCompetencyEvaluations_AutoIncrementId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Eval~",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropIndex(
                name: "IX_PortfolioVerificationLogs_ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropCheckConstraint(
                name: "CK_EvaluationForm_Status",
                table: "EvaluationForms");

            migrationBuilder.DropIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId_AssessmentP~",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropIndex(
                name: "IX_DepartmentStrategicPerformanceData_IndicatorSystemId",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropColumn(
                name: "EvaluatingManagerUserId",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "OverallComments",
                table: "StaffCompetencyEvaluations");

            migrationBuilder.DropColumn(
                name: "ChecklistItemDefinitionEntityId",
                table: "PortfolioVerificationLogs");

            migrationBuilder.DropColumn(
                name: "Weight",
                table: "FormCategories");

            migrationBuilder.DropColumn(
                name: "SubmittedDynamicDataInstanceId",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "ApplicableAcademicCadresJson",
                table: "EvaluationForms");

            migrationBuilder.DropColumn(
                name: "ActualValue",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "IndicatorSystemId",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "DepartmentStrategicPerformanceData");

            migrationBuilder.RenameColumn(
                name: "UploadedByUniveristyUserId",
                table: "EvidenceFiles",
                newName: "UploadedByUserId");

            migrationBuilder.RenameColumn(
                name: "StoredFilePath",
                table: "EvidenceFiles",
                newName: "FilePath");

            migrationBuilder.RenameColumn(
                name: "SizeBytes",
                table: "EvidenceFiles",
                newName: "FileSize");

            migrationBuilder.RenameColumn(
                name: "TargetValue",
                table: "DepartmentStrategicPerformanceData",
                newName: "Comments");

            migrationBuilder.RenameColumn(
                name: "RejectionComments",
                table: "AcademicSubmissions",
                newName: "RejectionReason");

            migrationBuilder.RenameColumn(
                name: "ApprovalComments",
                table: "AcademicSubmissions",
                newName: "Comments");

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedAt",
                table: "StaffCompetencyEvaluations",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApprovedByUserId",
                table: "StaffCompetencyEvaluations",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Comments",
                table: "StaffCompetencyEvaluations",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CompetencyArea",
                table: "StaffCompetencyEvaluations",
                type: "character varying(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "FinalScore",
                table: "StaffCompetencyEvaluations",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PeerAssessmentScore",
                table: "StaffCompetencyEvaluations",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "SelfAssessmentScore",
                table: "StaffCompetencyEvaluations",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SubmittedByUserId",
                table: "StaffCompetencyEvaluations",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "SupervisorAssessmentScore",
                table: "StaffCompetencyEvaluations",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "WeightPercentage",
                table: "FormCategories",
                type: "numeric(5,2)",
                precision: 5,
                scale: 2,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "SubmittedByUserId",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "character varying(256)",
                oldMaxLength: 256);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedAt",
                table: "DepartmentStrategicPerformanceData",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApprovedByUserId",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataKey",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DataType",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataValue",
                table: "DepartmentStrategicPerformanceData",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddUniqueConstraint(
                name: "AK_ChecklistItemDefinitions_ItemSystemId",
                table: "ChecklistItemDefinitions",
                column: "ItemSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Eval~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "AcademicianUniveristyUserId", "EvaluationContextId", "CompetencyArea" },
                unique: true);

            migrationBuilder.AddCheckConstraint(
                name: "CK_EvaluationForm_Status",
                table: "EvaluationForms",
                sql: "\"Status\" IN ('Draft', 'Active', 'Closed')");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId_AssessmentP~",
                table: "DepartmentStrategicPerformanceData",
                columns: new[] { "DepartmentId", "AssessmentPeriodId", "DataKey" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_ItemSyst~",
                table: "PortfolioVerificationLogs",
                column: "ItemSystemId",
                principalTable: "ChecklistItemDefinitions",
                principalColumn: "ItemSystemId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
