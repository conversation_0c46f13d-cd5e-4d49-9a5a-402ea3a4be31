﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ApdysPermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    PermissionName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApdysPermissions", x => x.Id);
                    table.UniqueConstraint("AK_ApdysPermissions_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "ApdysRoles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RoleName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApdysRoles", x => x.Id);
                    table.UniqueConstraint("AK_ApdysRoles_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "ChecklistItemDefinitions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ItemSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Category = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    RequiresEbysVerification = table.Column<bool>(type: "boolean", nullable: false),
                    EbysDataSourceHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChecklistItemDefinitions", x => x.Id);
                    table.UniqueConstraint("AK_ChecklistItemDefinitions_ItemSystemId", x => x.ItemSystemId);
                });

            migrationBuilder.CreateTable(
                name: "DepartmentStrategicPerformanceData",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    DepartmentId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AssessmentPeriodId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DataKey = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DataValue = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    DataType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SubmittedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DepartmentStrategicPerformanceData", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "EvaluationForms",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EvaluationPeriodStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EvaluationPeriodEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SubmissionDeadline = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvaluationForms", x => x.Id);
                    table.UniqueConstraint("AK_EvaluationForms_AutoIncrementId", x => x.AutoIncrementId);
                    table.CheckConstraint("CK_EvaluationForm_Status", "\"Status\" IN ('Draft', 'Active', 'Closed')");
                });

            migrationBuilder.CreateTable(
                name: "GenericDataEntryRecords",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    EntryTypeSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AssessmentPeriodId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DataKey = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DataValue = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    DataType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    EnteredAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    EnteredByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    VerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    VerifiedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GenericDataEntryRecords", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RlxEnums",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxEnums", x => x.Id);
                    table.UniqueConstraint("AK_RlxEnums_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "RlxLocalizations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ReferenceId = table.Column<string>(type: "text", nullable: false),
                    Culture = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Key = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Value = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxLocalizations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StaffCompetencyEvaluations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    EvaluationContextId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CompetencyArea = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    SelfAssessmentScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    SupervisorAssessmentScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    PeerAssessmentScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    FinalScore = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    SubmittedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaffCompetencyEvaluations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StaticCriterionDefinitions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    SystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DataType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DataSourceHint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CalculationLogic = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StaticCriterionDefinitions", x => x.Id);
                    table.UniqueConstraint("AK_StaticCriterionDefinitions_SystemId", x => x.SystemId);
                    table.CheckConstraint("CK_StaticCriterion_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                });

            migrationBuilder.CreateTable(
                name: "ApdysRolePermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    RoleAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    PermissionAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApdysRolePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ApdysRolePermissions_ApdysPermissions_PermissionAutoIncreme~",
                        column: x => x.PermissionAutoIncrementId,
                        principalTable: "ApdysPermissions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ApdysRolePermissions_ApdysRoles_RoleAutoIncrementId",
                        column: x => x.RoleAutoIncrementId,
                        principalTable: "ApdysRoles",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "UserApdysRoleMappings",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UniversityUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    RoleAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    AssignedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    AssignedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    RevokedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RevokedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserApdysRoleMappings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserApdysRoleMappings_ApdysRoles_RoleAutoIncrementId",
                        column: x => x.RoleAutoIncrementId,
                        principalTable: "ApdysRoles",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PortfolioVerificationLogs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    ItemSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    VerificationStatus = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    VerificationNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    LastVerifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastVerifiedByArchivistUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EbysReferenceId = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PortfolioVerificationLogs", x => x.Id);
                    table.CheckConstraint("CK_PortfolioVerification_Status", "\"VerificationStatus\" IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                    table.ForeignKey(
                        name: "FK_PortfolioVerificationLogs_ChecklistItemDefinitions_ItemSyst~",
                        column: x => x.ItemSystemId,
                        principalTable: "ChecklistItemDefinitions",
                        principalColumn: "ItemSystemId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "AcademicSubmissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicianUniveristyUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    EvaluationFormAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    SubmittedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApprovedByControllerUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    RejectedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RejectedByControllerUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    RejectionReason = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Comments = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicSubmissions", x => x.Id);
                    table.UniqueConstraint("AK_AcademicSubmissions_AutoIncrementId", x => x.AutoIncrementId);
                    table.CheckConstraint("CK_AcademicSubmission_Status", "\"Status\" IN ('Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'RequiresRevision')");
                    table.ForeignKey(
                        name: "FK_AcademicSubmissions_EvaluationForms_EvaluationFormAutoIncre~",
                        column: x => x.EvaluationFormAutoIncrementId,
                        principalTable: "EvaluationForms",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "FormCategories",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    EvaluationFormAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    WeightPercentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormCategories", x => x.Id);
                    table.UniqueConstraint("AK_FormCategories_AutoIncrementId", x => x.AutoIncrementId);
                    table.ForeignKey(
                        name: "FK_FormCategories_EvaluationForms_EvaluationFormAutoIncrementId",
                        column: x => x.EvaluationFormAutoIncrementId,
                        principalTable: "EvaluationForms",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RlxEnumValues",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EnumId = table.Column<int>(type: "integer", nullable: false),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Code = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    WhichRow = table.Column<int>(type: "integer", nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxEnumValues", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RlxEnumValues_RlxEnums_EnumId",
                        column: x => x.EnumId,
                        principalTable: "RlxEnums",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EvidenceFiles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AcademicSubmissionAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    FormCriterionLinkId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    FileName = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    FilePath = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: false),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    ContentType = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    UploadedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UploadedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EvidenceFiles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_EvidenceFiles_AcademicSubmissions_AcademicSubmissionAutoInc~",
                        column: x => x.AcademicSubmissionAutoIncrementId,
                        principalTable: "AcademicSubmissions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FormCriterionLinks",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    FormCategoryAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    StaticCriterionSystemId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DynamicCriterionTemplateId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CriterionType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DisplayOrder = table.Column<int>(type: "integer", nullable: false),
                    WeightPercentage = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FormCriterionLinks", x => x.Id);
                    table.CheckConstraint("CK_FormCriterionLink_CriterionType", "\"CriterionType\" IN ('Static', 'Dynamic')");
                    table.ForeignKey(
                        name: "FK_FormCriterionLinks_FormCategories_FormCategoryAutoIncrement~",
                        column: x => x.FormCategoryAutoIncrementId,
                        principalTable: "FormCategories",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                        column: x => x.StaticCriterionSystemId,
                        principalTable: "StaticCriterionDefinitions",
                        principalColumn: "SystemId",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AcademicianUniveristyUserId",
                table: "AcademicSubmissions",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AcademicianUniveristyUserId_EvaluationF~",
                table: "AcademicSubmissions",
                columns: new[] { "AcademicianUniveristyUserId", "EvaluationFormAutoIncrementId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_ApprovedByControllerUserId",
                table: "AcademicSubmissions",
                column: "ApprovedByControllerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_AutoIncrementId",
                table: "AcademicSubmissions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_EvaluationFormAutoIncrementId",
                table: "AcademicSubmissions",
                column: "EvaluationFormAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_RejectedByControllerUserId",
                table: "AcademicSubmissions",
                column: "RejectedByControllerUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_Status",
                table: "AcademicSubmissions",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicSubmissions_SubmittedAt",
                table: "AcademicSubmissions",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ApdysPermissions_AutoIncrementId",
                table: "ApdysPermissions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysPermissions_PermissionName",
                table: "ApdysPermissions",
                column: "PermissionName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRolePermissions_AutoIncrementId",
                table: "ApdysRolePermissions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRolePermissions_PermissionAutoIncrementId",
                table: "ApdysRolePermissions",
                column: "PermissionAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRolePermissions_RoleAutoIncrementId_PermissionAutoIncr~",
                table: "ApdysRolePermissions",
                columns: new[] { "RoleAutoIncrementId", "PermissionAutoIncrementId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRoles_AutoIncrementId",
                table: "ApdysRoles",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRoles_RoleName",
                table: "ApdysRoles",
                column: "RoleName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChecklistItemDefinitions_AutoIncrementId",
                table: "ChecklistItemDefinitions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ChecklistItemDefinitions_Category",
                table: "ChecklistItemDefinitions",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_ChecklistItemDefinitions_ItemSystemId",
                table: "ChecklistItemDefinitions",
                column: "ItemSystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_AssessmentPeriodId",
                table: "DepartmentStrategicPerformanceData",
                column: "AssessmentPeriodId");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_AutoIncrementId",
                table: "DepartmentStrategicPerformanceData",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId",
                table: "DepartmentStrategicPerformanceData",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_DepartmentId_AssessmentP~",
                table: "DepartmentStrategicPerformanceData",
                columns: new[] { "DepartmentId", "AssessmentPeriodId", "DataKey" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DepartmentStrategicPerformanceData_SubmittedAt",
                table: "DepartmentStrategicPerformanceData",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_AutoIncrementId",
                table: "EvaluationForms",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_EvaluationPeriodEndDate",
                table: "EvaluationForms",
                column: "EvaluationPeriodEndDate");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_EvaluationPeriodStartDate",
                table: "EvaluationForms",
                column: "EvaluationPeriodStartDate");

            migrationBuilder.CreateIndex(
                name: "IX_EvaluationForms_Status",
                table: "EvaluationForms",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_AcademicSubmissionAutoIncrementId",
                table: "EvidenceFiles",
                column: "AcademicSubmissionAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_AutoIncrementId",
                table: "EvidenceFiles",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_UploadedAt",
                table: "EvidenceFiles",
                column: "UploadedAt");

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_AutoIncrementId",
                table: "FormCategories",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_EvaluationFormAutoIncrementId",
                table: "FormCategories",
                column: "EvaluationFormAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_FormCategories_EvaluationFormAutoIncrementId_DisplayOrder",
                table: "FormCategories",
                columns: new[] { "EvaluationFormAutoIncrementId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_AutoIncrementId",
                table: "FormCriterionLinks",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_FormCategoryAutoIncrementId",
                table: "FormCriterionLinks",
                column: "FormCategoryAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_FormCategoryAutoIncrementId_DisplayOrder",
                table: "FormCriterionLinks",
                columns: new[] { "FormCategoryAutoIncrementId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_StaticCriterionSystemId",
                table: "FormCriterionLinks",
                column: "StaticCriterionSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_AcademicianUniveristyUserId",
                table: "GenericDataEntryRecords",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_AcademicianUniveristyUserId_EntryTy~",
                table: "GenericDataEntryRecords",
                columns: new[] { "AcademicianUniveristyUserId", "EntryTypeSystemId", "AssessmentPeriodId", "DataKey" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_AutoIncrementId",
                table: "GenericDataEntryRecords",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_EnteredAt",
                table: "GenericDataEntryRecords",
                column: "EnteredAt");

            migrationBuilder.CreateIndex(
                name: "IX_GenericDataEntryRecords_EntryTypeSystemId",
                table: "GenericDataEntryRecords",
                column: "EntryTypeSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_AcademicianUniveristyUserId",
                table: "PortfolioVerificationLogs",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_AcademicianUniveristyUserId_ItemS~",
                table: "PortfolioVerificationLogs",
                columns: new[] { "AcademicianUniveristyUserId", "ItemSystemId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_AutoIncrementId",
                table: "PortfolioVerificationLogs",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_ItemSystemId",
                table: "PortfolioVerificationLogs",
                column: "ItemSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_LastVerifiedByArchivistUserId",
                table: "PortfolioVerificationLogs",
                column: "LastVerifiedByArchivistUserId");

            migrationBuilder.CreateIndex(
                name: "IX_PortfolioVerificationLogs_VerificationStatus",
                table: "PortfolioVerificationLogs",
                column: "VerificationStatus");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnums_AutoIncrementId",
                table: "RlxEnums",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_AutoIncrementId",
                table: "RlxEnumValues",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxEnumValues_EnumId",
                table: "RlxEnumValues",
                column: "EnumId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxLocalizations_AutoIncrementId",
                table: "RlxLocalizations",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId",
                table: "StaffCompetencyEvaluations",
                column: "AcademicianUniveristyUserId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AcademicianUniveristyUserId_Eval~",
                table: "StaffCompetencyEvaluations",
                columns: new[] { "AcademicianUniveristyUserId", "EvaluationContextId", "CompetencyArea" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_AutoIncrementId",
                table: "StaffCompetencyEvaluations",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_EvaluationContextId",
                table: "StaffCompetencyEvaluations",
                column: "EvaluationContextId");

            migrationBuilder.CreateIndex(
                name: "IX_StaffCompetencyEvaluations_SubmittedAt",
                table: "StaffCompetencyEvaluations",
                column: "SubmittedAt");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionDefinitions_AutoIncrementId",
                table: "StaticCriterionDefinitions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionDefinitions_SystemId",
                table: "StaticCriterionDefinitions",
                column: "SystemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_AssignedAt",
                table: "UserApdysRoleMappings",
                column: "AssignedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_AutoIncrementId",
                table: "UserApdysRoleMappings",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_RoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                column: "RoleAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_UserApdysRoleMappings_UniversityUserId_RoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                columns: new[] { "UniversityUserId", "RoleAutoIncrementId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ApdysRolePermissions");

            migrationBuilder.DropTable(
                name: "DepartmentStrategicPerformanceData");

            migrationBuilder.DropTable(
                name: "EvidenceFiles");

            migrationBuilder.DropTable(
                name: "FormCriterionLinks");

            migrationBuilder.DropTable(
                name: "GenericDataEntryRecords");

            migrationBuilder.DropTable(
                name: "PortfolioVerificationLogs");

            migrationBuilder.DropTable(
                name: "RlxEnumValues");

            migrationBuilder.DropTable(
                name: "RlxLocalizations");

            migrationBuilder.DropTable(
                name: "StaffCompetencyEvaluations");

            migrationBuilder.DropTable(
                name: "UserApdysRoleMappings");

            migrationBuilder.DropTable(
                name: "ApdysPermissions");

            migrationBuilder.DropTable(
                name: "AcademicSubmissions");

            migrationBuilder.DropTable(
                name: "FormCategories");

            migrationBuilder.DropTable(
                name: "StaticCriterionDefinitions");

            migrationBuilder.DropTable(
                name: "ChecklistItemDefinitions");

            migrationBuilder.DropTable(
                name: "RlxEnums");

            migrationBuilder.DropTable(
                name: "ApdysRoles");

            migrationBuilder.DropTable(
                name: "EvaluationForms");
        }
    }
}
