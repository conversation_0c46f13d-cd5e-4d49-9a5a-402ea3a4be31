﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class Phase3StaticCriterionRefactor : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_StaticCriterionDefinitions_SystemId",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_StaticCriterionDefinitions",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionDefinitions_AutoIncrementId",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropIndex(
                name: "IX_FormCriterionLinks_StaticCriterionSystemId",
                table: "FormCriterionLinks");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropColumn(
                name: "AutoIncrementId",
                table: "StaticCriterionDefinitions");

            migrationBuilder.RenameColumn(
                name: "SystemId",
                table: "StaticCriterionDefinitions",
                newName: "StaticCriterionSystemId");

            migrationBuilder.RenameIndex(
                name: "IX_StaticCriterionDefinitions_SystemId",
                table: "StaticCriterionDefinitions",
                newName: "IX_StaticCriterionDefinitions_StaticCriterionSystemId");

            migrationBuilder.AddColumn<string>(
                name: "StaticCriterionDefinitionStaticCriterionSystemId",
                table: "FormCriterionLinks",
                type: "character varying(100)",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_StaticCriterionDefinitions",
                table: "StaticCriterionDefinitions",
                column: "StaticCriterionSystemId");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionDefinitions_IsActive",
                table: "StaticCriterionDefinitions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_StaticCriterionDefinitionStaticCriterion~",
                table: "FormCriterionLinks",
                column: "StaticCriterionDefinitionStaticCriterionSystemId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks",
                column: "StaticCriterionDefinitionStaticCriterionSystemId",
                principalTable: "StaticCriterionDefinitions",
                principalColumn: "StaticCriterionSystemId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks");

            migrationBuilder.DropPrimaryKey(
                name: "PK_StaticCriterionDefinitions",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropIndex(
                name: "IX_StaticCriterionDefinitions_IsActive",
                table: "StaticCriterionDefinitions");

            migrationBuilder.DropIndex(
                name: "IX_FormCriterionLinks_StaticCriterionDefinitionStaticCriterion~",
                table: "FormCriterionLinks");

            migrationBuilder.DropColumn(
                name: "StaticCriterionDefinitionStaticCriterionSystemId",
                table: "FormCriterionLinks");

            migrationBuilder.RenameColumn(
                name: "StaticCriterionSystemId",
                table: "StaticCriterionDefinitions",
                newName: "SystemId");

            migrationBuilder.RenameIndex(
                name: "IX_StaticCriterionDefinitions_StaticCriterionSystemId",
                table: "StaticCriterionDefinitions",
                newName: "IX_StaticCriterionDefinitions_SystemId");

            migrationBuilder.AddColumn<string>(
                name: "Id",
                table: "StaticCriterionDefinitions",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "AutoIncrementId",
                table: "StaticCriterionDefinitions",
                type: "integer",
                nullable: false,
                defaultValue: 0)
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_StaticCriterionDefinitions_SystemId",
                table: "StaticCriterionDefinitions",
                column: "SystemId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_StaticCriterionDefinitions",
                table: "StaticCriterionDefinitions",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_StaticCriterionDefinitions_AutoIncrementId",
                table: "StaticCriterionDefinitions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_StaticCriterionSystemId",
                table: "FormCriterionLinks",
                column: "StaticCriterionSystemId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks",
                column: "StaticCriterionSystemId",
                principalTable: "StaticCriterionDefinitions",
                principalColumn: "SystemId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
