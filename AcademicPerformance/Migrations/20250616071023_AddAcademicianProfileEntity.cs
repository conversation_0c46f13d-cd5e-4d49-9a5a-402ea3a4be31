﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class AddAcademicianProfileEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ApdysRolePermissions");

            migrationBuilder.DropTable(
                name: "RlxLocalizations");

            migrationBuilder.DropTable(
                name: "ApdysPermissions");

            migrationBuilder.CreateTable(
                name: "AcademicianProfiles",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UniversityUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Surname = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FullName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Department = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    AcademicCadre = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Email = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Title = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LastSyncedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    SyncNotes = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedByUserId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AcademicianProfiles", x => x.Id);
                    table.UniqueConstraint("AK_AcademicianProfiles_UniversityUserId", x => x.UniversityUserId);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_AcademicCadre",
                table: "AcademicianProfiles",
                column: "AcademicCadre");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_AutoIncrementId",
                table: "AcademicianProfiles",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_Department",
                table: "AcademicianProfiles",
                column: "Department");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_IsActive",
                table: "AcademicianProfiles",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_LastSyncedAt",
                table: "AcademicianProfiles",
                column: "LastSyncedAt");

            migrationBuilder.CreateIndex(
                name: "IX_AcademicianProfiles_UniversityUserId",
                table: "AcademicianProfiles",
                column: "UniversityUserId",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_AcademicSubmissions_AcademicianProfiles_AcademicianUniveris~",
                table: "AcademicSubmissions",
                column: "AcademicianUniveristyUserId",
                principalTable: "AcademicianProfiles",
                principalColumn: "UniversityUserId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AcademicSubmissions_AcademicianProfiles_AcademicianUniveris~",
                table: "AcademicSubmissions");

            migrationBuilder.DropTable(
                name: "AcademicianProfiles");

            migrationBuilder.CreateTable(
                name: "ApdysPermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    PermissionName = table.Column<string>(type: "character varying(150)", maxLength: 150, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApdysPermissions", x => x.Id);
                    table.UniqueConstraint("AK_ApdysPermissions_AutoIncrementId", x => x.AutoIncrementId);
                });

            migrationBuilder.CreateTable(
                name: "RlxLocalizations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Culture = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Key = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    ReferenceId = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxLocalizations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ApdysRolePermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    ApdysPermissionAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    ApdysRoleAutoIncrementId = table.Column<int>(type: "integer", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ApdysRolePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ApdysRolePermissions_ApdysPermissions_ApdysPermissionAutoIn~",
                        column: x => x.ApdysPermissionAutoIncrementId,
                        principalTable: "ApdysPermissions",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ApdysRolePermissions_ApdysRoles_ApdysRoleAutoIncrementId",
                        column: x => x.ApdysRoleAutoIncrementId,
                        principalTable: "ApdysRoles",
                        principalColumn: "AutoIncrementId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ApdysPermissions_AutoIncrementId",
                table: "ApdysPermissions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysPermissions_PermissionName",
                table: "ApdysPermissions",
                column: "PermissionName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRolePermissions_ApdysPermissionAutoIncrementId",
                table: "ApdysRolePermissions",
                column: "ApdysPermissionAutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRolePermissions_ApdysRoleAutoIncrementId_ApdysPermissi~",
                table: "ApdysRolePermissions",
                columns: new[] { "ApdysRoleAutoIncrementId", "ApdysPermissionAutoIncrementId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ApdysRolePermissions_AutoIncrementId",
                table: "ApdysRolePermissions",
                column: "AutoIncrementId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_RlxLocalizations_AutoIncrementId",
                table: "RlxLocalizations",
                column: "AutoIncrementId");
        }
    }
}
