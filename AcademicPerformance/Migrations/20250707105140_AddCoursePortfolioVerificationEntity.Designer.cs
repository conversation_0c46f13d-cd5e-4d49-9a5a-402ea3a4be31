﻿// <auto-generated />
using System;
using AcademicPerformance.DbContexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations
{
    [DbContext(typeof(AcademicPerformanceDbContext))]
    [Migration("20250707105140_AddCoursePortfolioVerificationEntity")]
    partial class AddCoursePortfolioVerificationEntity
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ApprovalComments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedByControllerUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EvaluationFormAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("RejectedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RejectedByControllerUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("RejectionComments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("ApprovedByControllerUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationFormAutoIncrementId");

                    b.HasIndex("RejectedByControllerUserId");

                    b.HasIndex("Status");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("AcademicianUniveristyUserId", "EvaluationFormAutoIncrementId")
                        .IsUnique();

                    b.ToTable("AcademicSubmissions", t =>
                        {
                            t.HasCheckConstraint("CK_AcademicSubmission_Status", "\"Status\" IN ('Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'RequiresRevision')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicCadre")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Department")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Email")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("FullName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastSyncedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Surname")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SyncNotes")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Title")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("UniversityUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicCadre");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("Department");

                    b.HasIndex("IsActive");

                    b.HasIndex("LastSyncedAt");

                    b.HasIndex("UniversityUserId")
                        .IsUnique();

                    b.ToTable("AcademicianProfiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("RoleName")
                        .IsUnique();

                    b.ToTable("ApdysRoles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CompetencyRatingEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("CompetencySystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Rating")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("StaffCompetencyEvaluationAutoIncrementId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CompetencySystemId");

                    b.HasIndex("StaffCompetencyEvaluationAutoIncrementId");

                    b.HasIndex("StaffCompetencyEvaluationAutoIncrementId", "CompetencySystemId")
                        .IsUnique();

                    b.ToTable("CompetencyRatings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CoursePortfolioVerificationEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianProfileId")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianTc")
                        .IsRequired()
                        .HasMaxLength(11)
                        .HasColumnType("character varying(11)");

                    b.Property<string>("AnswerKeyStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("AttendanceSheetStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("CourseCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CourseName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("CourseSyllabusStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EbysReferenceId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ExamPapersStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("ExamRecordStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<DateTime?>("LastVerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastVerifiedByArchivistId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("MakeupExamGradesStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("PeriodName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("UzemRecordsStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("WeeklyAttendanceStatus")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasDefaultValue("Pending");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianProfileId");

                    b.HasIndex("AcademicianTc");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("CourseCode");

                    b.HasIndex("LastVerifiedAt");

                    b.HasIndex("LastVerifiedByArchivistId");

                    b.HasIndex("PeriodName");

                    b.HasIndex("AcademicianTc", "CourseCode", "PeriodName")
                        .IsUnique();

                    b.ToTable("CoursePortfolioVerifications", t =>
                        {
                            t.HasCheckConstraint("CK_CoursePortfolioVerification_AnswerKeyStatus", "\"AnswerKeyStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_AttendanceSheetStatus", "\"AttendanceSheetStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_CourseSyllabusStatus", "\"CourseSyllabusStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_ExamPapersStatus", "\"ExamPapersStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_ExamRecordStatus", "\"ExamRecordStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_MakeupExamGradesStatus", "\"MakeupExamGradesStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_UzemRecordsStatus", "\"UzemRecordsStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");

                            t.HasCheckConstraint("CK_CoursePortfolioVerification_WeeklyAttendanceStatus", "\"WeeklyAttendanceStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicIndicatorDefinitionEntity", b =>
                {
                    b.Property<string>("IndicatorSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHigherBetter")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.HasKey("IndicatorSystemId");

                    b.HasIndex("IndicatorSystemId")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.ToTable("DepartmentStrategicIndicatorDefinitions", t =>
                        {
                            t.HasCheckConstraint("CK_DepartmentStrategicIndicator_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicPerformanceDataEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ActualValue")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("AssessmentPeriodId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DepartmentId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("IndicatorSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubmittedByUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("TargetValue")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.HasKey("Id");

                    b.HasIndex("AssessmentPeriodId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("DepartmentId");

                    b.HasIndex("IndicatorSystemId");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("DepartmentId", "AssessmentPeriodId", "IndicatorSystemId")
                        .IsUnique();

                    b.ToTable("DepartmentStrategicPerformanceData");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("ApplicableAcademicCadresJson")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("EvaluationPeriodEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("EvaluationPeriodStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("SubmissionDeadline")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationPeriodEndDate");

                    b.HasIndex("EvaluationPeriodStartDate");

                    b.HasIndex("Status");

                    b.ToTable("EvaluationForms", t =>
                        {
                            t.HasCheckConstraint("CK_EvaluationForm_Status", "\"Status\" IN ('Draft', 'Active', 'Archived')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvidenceFileEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AcademicSubmissionAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("ContentType")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("FormCriterionLinkId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<long>("SizeBytes")
                        .HasColumnType("bigint");

                    b.Property<string>("StoredFilePath")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("SubmittedDynamicDataInstanceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("UploadedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UploadedByUniveristyUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicSubmissionAutoIncrementId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("UploadedAt");

                    b.ToTable("EvidenceFiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<int>("EvaluationFormAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<double>("Weight")
                        .HasPrecision(5, 4)
                        .HasColumnType("double precision");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationFormAutoIncrementId");

                    b.HasIndex("EvaluationFormAutoIncrementId", "DisplayOrder");

                    b.ToTable("FormCategories");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("CriterionType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<string>("DynamicCriterionTemplateId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("FormCategoryAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("StaticCriterionSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("WeightPercentage")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("FormCategoryAutoIncrementId");

                    b.HasIndex("StaticCriterionSystemId");

                    b.HasIndex("FormCategoryAutoIncrementId", "DisplayOrder");

                    b.ToTable("FormCriterionLinks", t =>
                        {
                            t.HasCheckConstraint("CK_FormCriterionLink_CriterionType", "\"CriterionType\" IN ('Static', 'Dynamic')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryDefinitionEntity", b =>
                {
                    b.Property<string>("EntryTypeSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("DataStructureHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("ResponsibleRole")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("EntryTypeSystemId");

                    b.HasIndex("EntryTypeSystemId")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.HasIndex("ResponsibleRole");

                    b.ToTable("GenericDataEntryDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryRecordEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("AssessmentPeriodId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("DataKey")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DataType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DataValue")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("EnteredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("EnteredByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EntryTypeSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("VerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("VerifiedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EnteredAt");

                    b.HasIndex("EntryTypeSystemId");

                    b.HasIndex("AcademicianUniveristyUserId", "EntryTypeSystemId", "AssessmentPeriodId", "DataKey")
                        .IsUnique();

                    b.ToTable("GenericDataEntryRecords");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioChecklistItemDefinitionEntity", b =>
                {
                    b.Property<string>("ItemSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("EbysDataSourceHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<bool>("RequiresEbysVerification")
                        .HasColumnType("boolean");

                    b.HasKey("ItemSystemId");

                    b.HasIndex("Category");

                    b.HasIndex("IsActive");

                    b.HasIndex("ItemSystemId")
                        .IsUnique();

                    b.ToTable("PortfolioChecklistItemDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioVerificationLogEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EbysReferenceId")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ItemSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastVerifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastVerifiedByArchivistUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("VerificationNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("VerificationStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("ItemSystemId");

                    b.HasIndex("LastVerifiedByArchivistUserId");

                    b.HasIndex("VerificationStatus");

                    b.HasIndex("AcademicianUniveristyUserId", "ItemSystemId")
                        .IsUnique();

                    b.ToTable("PortfolioVerificationLogs", t =>
                        {
                            t.HasCheckConstraint("CK_PortfolioVerification_Status", "\"VerificationStatus\" IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyDefinitionEntity", b =>
                {
                    b.Property<string>("CompetencySystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("character varying(250)");

                    b.Property<string>("RatingScaleJson")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.HasKey("CompetencySystemId");

                    b.HasIndex("CompetencySystemId")
                        .IsUnique();

                    b.HasIndex("IsActive");

                    b.ToTable("StaffCompetencyDefinitions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("AcademicianUniveristyUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("EvaluatingManagerUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("EvaluationContextId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("OverallComments")
                        .HasMaxLength(4000)
                        .HasColumnType("character varying(4000)");

                    b.Property<DateTime>("SubmittedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("AcademicianUniveristyUserId");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("EvaluationContextId");

                    b.HasIndex("SubmittedAt");

                    b.HasIndex("AcademicianUniveristyUserId", "EvaluationContextId")
                        .IsUnique();

                    b.ToTable("StaffCompetencyEvaluations");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionCoefficientEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ApprovedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ApprovedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Category")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal>("Coefficient")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("CoefficientHistory")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CriterionName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DataSource")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("DisplayOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsApproved")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastUpdated")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastUpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("MaximumLimit")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("MinimumLimit")
                        .HasColumnType("decimal(18,4)");

                    b.Property<byte[]>("RowVersion")
                        .IsConcurrencyToken()
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("bytea");

                    b.Property<string>("StaticCriterionSystemId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("StaticCriterionCoefficients");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", b =>
                {
                    b.Property<string>("StaticCriterionSystemId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CalculationLogic")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("DataSourceHint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("StaticCriterionSystemId");

                    b.HasIndex("IsActive");

                    b.HasIndex("StaticCriterionSystemId")
                        .IsUnique();

                    b.ToTable("StaticCriterionDefinitions", t =>
                        {
                            t.HasCheckConstraint("CK_StaticCriterion_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')");
                        });
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.UserApdysRoleMappingEntity", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("ApdysRoleAutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<DateTime>("AssignedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AssignedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<DateTime?>("RevokedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("RevokedByUserId")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("UniversityUserId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("ApdysRoleAutoIncrementId");

                    b.HasIndex("AssignedAt");

                    b.HasIndex("AutoIncrementId")
                        .IsUnique();

                    b.HasIndex("UniversityUserId", "ApdysRoleAutoIncrementId")
                        .IsUnique();

                    b.ToTable("UserApdysRoleMappings");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.ToTable("RlxEnums");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("AutoIncrementId"));

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<int>("EnumId")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<int?>("WhichRow")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AutoIncrementId");

                    b.HasIndex("EnumId");

                    b.ToTable("RlxEnumValues");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxLocalizationDbContextModels.RlxLocalization", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("AutoIncrementId")
                        .HasColumnType("integer");

                    b.Property<string>("Culture")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ReferenceId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable((string)null);

                    b.ToView("RlxLocalizations", (string)null);
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", "AcademicianProfile")
                        .WithMany("Submissions")
                        .HasForeignKey("AcademicianUniveristyUserId")
                        .HasPrincipalKey("UniversityUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany("Submissions")
                        .HasForeignKey("EvaluationFormAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("AcademicianProfile");

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CompetencyRatingEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyDefinitionEntity", "CompetencyDefinition")
                        .WithMany("CompetencyRatings")
                        .HasForeignKey("CompetencySystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", "Evaluation")
                        .WithMany("CompetencyRatings")
                        .HasForeignKey("StaffCompetencyEvaluationAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompetencyDefinition");

                    b.Navigation("Evaluation");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.CoursePortfolioVerificationEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", "AcademicianProfile")
                        .WithMany()
                        .HasForeignKey("AcademicianProfileId");

                    b.Navigation("AcademicianProfile");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicPerformanceDataEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicIndicatorDefinitionEntity", "IndicatorDefinition")
                        .WithMany("PerformanceData")
                        .HasForeignKey("IndicatorSystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("IndicatorDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvidenceFileEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", "AcademicSubmission")
                        .WithMany("EvidenceFiles")
                        .HasForeignKey("AcademicSubmissionAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AcademicSubmission");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", "EvaluationForm")
                        .WithMany("Categories")
                        .HasForeignKey("EvaluationFormAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EvaluationForm");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCriterionLinkEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", "FormCategory")
                        .WithMany("CriterionLinks")
                        .HasForeignKey("FormCategoryAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", "StaticCriterionDefinition")
                        .WithMany("FormCriterionLinks")
                        .HasForeignKey("StaticCriterionSystemId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("FormCategory");

                    b.Navigation("StaticCriterionDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryRecordEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryDefinitionEntity", "EntryTypeDefinition")
                        .WithMany("DataEntryRecords")
                        .HasForeignKey("EntryTypeSystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("EntryTypeDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioVerificationLogEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioChecklistItemDefinitionEntity", "ChecklistItemDefinition")
                        .WithMany("VerificationLogs")
                        .HasForeignKey("ItemSystemId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ChecklistItemDefinition");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.UserApdysRoleMappingEntity", b =>
                {
                    b.HasOne("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", "Role")
                        .WithMany("UserMappings")
                        .HasForeignKey("ApdysRoleAutoIncrementId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnumValue", b =>
                {
                    b.HasOne("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", "RlxEnum")
                        .WithMany("RlxEnumValues")
                        .HasForeignKey("EnumId")
                        .HasPrincipalKey("AutoIncrementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RlxEnum");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicSubmissionEntity", b =>
                {
                    b.Navigation("EvidenceFiles");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.AcademicianProfileEntity", b =>
                {
                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.ApdysRoleEntity", b =>
                {
                    b.Navigation("UserMappings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.DepartmentStrategicIndicatorDefinitionEntity", b =>
                {
                    b.Navigation("PerformanceData");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.EvaluationFormEntity", b =>
                {
                    b.Navigation("Categories");

                    b.Navigation("Submissions");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.FormCategoryEntity", b =>
                {
                    b.Navigation("CriterionLinks");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.GenericDataEntryDefinitionEntity", b =>
                {
                    b.Navigation("DataEntryRecords");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.PortfolioChecklistItemDefinitionEntity", b =>
                {
                    b.Navigation("VerificationLogs");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyDefinitionEntity", b =>
                {
                    b.Navigation("CompetencyRatings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaffCompetencyEvaluationEntity", b =>
                {
                    b.Navigation("CompetencyRatings");
                });

            modelBuilder.Entity("AcademicPerformance.Models.AcademicPerformanceDbContextModels.StaticCriterionDefinitionEntity", b =>
                {
                    b.Navigation("FormCriterionLinks");
                });

            modelBuilder.Entity("Rlx.Shared.Models.RlxEnumDbContextModels.RlxEnum", b =>
                {
                    b.Navigation("RlxEnumValues");
                });
#pragma warning restore 612, 618
        }
    }
}
