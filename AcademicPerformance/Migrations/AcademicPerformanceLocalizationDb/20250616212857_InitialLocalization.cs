﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace AcademicPerformance.Migrations.AcademicPerformanceLocalizationDb
{
    /// <inheritdoc />
    public partial class InitialLocalization : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RlxLocalizations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    AutoIncrementId = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    ReferenceId = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Culture = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Key = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RlxLocalizations", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RlxLocalizations_AutoIncrementId",
                table: "RlxLocalizations",
                column: "AutoIncrementId");

            migrationBuilder.CreateIndex(
                name: "IX_RlxLocalizations_ReferenceId_Culture_Key",
                table: "RlxLocalizations",
                columns: new[] { "ReferenceId", "Culture", "Key" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RlxLocalizations");
        }
    }
}
