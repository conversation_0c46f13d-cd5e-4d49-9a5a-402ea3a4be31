﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class AddMinIOFieldsToEvidenceFileEntity : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AccessUrl",
                table: "EvidenceFiles",
                type: "character varying(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileChecksum",
                table: "EvidenceFiles",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MinioBucketName",
                table: "EvidenceFiles",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MinioETag",
                table: "EvidenceFiles",
                type: "character varying(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MinioObjectName",
                table: "EvidenceFiles",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "MinioPresignedUrlExpiry",
                table: "EvidenceFiles",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "OriginalFileName",
                table: "EvidenceFiles",
                type: "character varying(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "StorageType",
                table: "EvidenceFiles",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                defaultValue: "Local");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_MinioBucketName",
                table: "EvidenceFiles",
                column: "MinioBucketName");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_MinioObjectName",
                table: "EvidenceFiles",
                column: "MinioObjectName");

            migrationBuilder.CreateIndex(
                name: "IX_EvidenceFiles_StorageType",
                table: "EvidenceFiles",
                column: "StorageType");

            migrationBuilder.AddCheckConstraint(
                name: "CK_EvidenceFile_Size",
                table: "EvidenceFiles",
                sql: "\"SizeBytes\" > 0");

            migrationBuilder.AddCheckConstraint(
                name: "CK_EvidenceFile_StorageType",
                table: "EvidenceFiles",
                sql: "\"StorageType\" IN ('Local', 'MinIO')");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_EvidenceFiles_MinioBucketName",
                table: "EvidenceFiles");

            migrationBuilder.DropIndex(
                name: "IX_EvidenceFiles_MinioObjectName",
                table: "EvidenceFiles");

            migrationBuilder.DropIndex(
                name: "IX_EvidenceFiles_StorageType",
                table: "EvidenceFiles");

            migrationBuilder.DropCheckConstraint(
                name: "CK_EvidenceFile_Size",
                table: "EvidenceFiles");

            migrationBuilder.DropCheckConstraint(
                name: "CK_EvidenceFile_StorageType",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "AccessUrl",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "FileChecksum",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "MinioBucketName",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "MinioETag",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "MinioObjectName",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "MinioPresignedUrlExpiry",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "OriginalFileName",
                table: "EvidenceFiles");

            migrationBuilder.DropColumn(
                name: "StorageType",
                table: "EvidenceFiles");
        }
    }
}
