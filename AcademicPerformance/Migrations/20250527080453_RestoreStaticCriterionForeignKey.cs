﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class RestoreStaticCriterionForeignKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks");

            migrationBuilder.DropIndex(
                name: "IX_FormCriterionLinks_StaticCriterionDefinitionStaticCriterion~",
                table: "FormCriterionLinks");

            migrationBuilder.DropColumn(
                name: "StaticCriterionDefinitionStaticCriterionSystemId",
                table: "FormCriterionLinks");

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_StaticCriterionSystemId",
                table: "FormCriterionLinks",
                column: "StaticCriterionSystemId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks",
                column: "StaticCriterionSystemId",
                principalTable: "StaticCriterionDefinitions",
                principalColumn: "StaticCriterionSystemId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks");

            migrationBuilder.DropIndex(
                name: "IX_FormCriterionLinks_StaticCriterionSystemId",
                table: "FormCriterionLinks");

            migrationBuilder.AddColumn<string>(
                name: "StaticCriterionDefinitionStaticCriterionSystemId",
                table: "FormCriterionLinks",
                type: "character varying(100)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_FormCriterionLinks_StaticCriterionDefinitionStaticCriterion~",
                table: "FormCriterionLinks",
                column: "StaticCriterionDefinitionStaticCriterionSystemId");

            migrationBuilder.AddForeignKey(
                name: "FK_FormCriterionLinks_StaticCriterionDefinitions_StaticCriteri~",
                table: "FormCriterionLinks",
                column: "StaticCriterionDefinitionStaticCriterionSystemId",
                principalTable: "StaticCriterionDefinitions",
                principalColumn: "StaticCriterionSystemId");
        }
    }
}
