﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AcademicPerformance.Migrations
{
    /// <inheritdoc />
    public partial class Phase2HighPriorityImplementation : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApdysRolePermissions_ApdysPermissions_PermissionAutoIncreme~",
                table: "ApdysRolePermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_ApdysRolePermissions_ApdysRoles_RoleAutoIncrementId",
                table: "ApdysRolePermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserApdysRoleMappings_ApdysRoles_RoleAutoIncrementId",
                table: "UserApdysRoleMappings");

            migrationBuilder.RenameColumn(
                name: "RoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                newName: "ApdysRoleAutoIncrementId");

            migrationBuilder.RenameIndex(
                name: "IX_UserApdysRoleMappings_UniversityUserId_RoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                newName: "IX_UserApdysRoleMappings_UniversityUserId_ApdysRoleAutoIncreme~");

            migrationBuilder.RenameIndex(
                name: "IX_UserApdysRoleMappings_RoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                newName: "IX_UserApdysRoleMappings_ApdysRoleAutoIncrementId");

            migrationBuilder.RenameColumn(
                name: "RoleAutoIncrementId",
                table: "ApdysRolePermissions",
                newName: "ApdysRoleAutoIncrementId");

            migrationBuilder.RenameColumn(
                name: "PermissionAutoIncrementId",
                table: "ApdysRolePermissions",
                newName: "ApdysPermissionAutoIncrementId");

            migrationBuilder.RenameIndex(
                name: "IX_ApdysRolePermissions_RoleAutoIncrementId_PermissionAutoIncr~",
                table: "ApdysRolePermissions",
                newName: "IX_ApdysRolePermissions_ApdysRoleAutoIncrementId_ApdysPermissi~");

            migrationBuilder.RenameIndex(
                name: "IX_ApdysRolePermissions_PermissionAutoIncrementId",
                table: "ApdysRolePermissions",
                newName: "IX_ApdysRolePermissions_ApdysPermissionAutoIncrementId");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "FormCriterionLinks",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CreatedByUserId",
                table: "FormCriterionLinks",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "FormCategories",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "CreatedByUserId",
                table: "FormCategories",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UpdatedAt",
                table: "FormCategories",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "UpdatedByUserId",
                table: "FormCategories",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "ApdysRoles",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "Deleted",
                table: "ApdysRoles",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "PermissionName",
                table: "ApdysPermissions",
                type: "character varying(150)",
                maxLength: 150,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(100)",
                oldMaxLength: 100);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "ApdysPermissions",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<bool>(
                name: "Deleted",
                table: "ApdysPermissions",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_ApdysRolePermissions_ApdysPermissions_ApdysPermissionAutoIn~",
                table: "ApdysRolePermissions",
                column: "ApdysPermissionAutoIncrementId",
                principalTable: "ApdysPermissions",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ApdysRolePermissions_ApdysRoles_ApdysRoleAutoIncrementId",
                table: "ApdysRolePermissions",
                column: "ApdysRoleAutoIncrementId",
                principalTable: "ApdysRoles",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserApdysRoleMappings_ApdysRoles_ApdysRoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                column: "ApdysRoleAutoIncrementId",
                principalTable: "ApdysRoles",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ApdysRolePermissions_ApdysPermissions_ApdysPermissionAutoIn~",
                table: "ApdysRolePermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_ApdysRolePermissions_ApdysRoles_ApdysRoleAutoIncrementId",
                table: "ApdysRolePermissions");

            migrationBuilder.DropForeignKey(
                name: "FK_UserApdysRoleMappings_ApdysRoles_ApdysRoleAutoIncrementId",
                table: "UserApdysRoleMappings");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "FormCriterionLinks");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "FormCriterionLinks");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "FormCategories");

            migrationBuilder.DropColumn(
                name: "CreatedByUserId",
                table: "FormCategories");

            migrationBuilder.DropColumn(
                name: "UpdatedAt",
                table: "FormCategories");

            migrationBuilder.DropColumn(
                name: "UpdatedByUserId",
                table: "FormCategories");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "ApdysRoles");

            migrationBuilder.DropColumn(
                name: "Deleted",
                table: "ApdysRoles");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "ApdysPermissions");

            migrationBuilder.DropColumn(
                name: "Deleted",
                table: "ApdysPermissions");

            migrationBuilder.RenameColumn(
                name: "ApdysRoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                newName: "RoleAutoIncrementId");

            migrationBuilder.RenameIndex(
                name: "IX_UserApdysRoleMappings_UniversityUserId_ApdysRoleAutoIncreme~",
                table: "UserApdysRoleMappings",
                newName: "IX_UserApdysRoleMappings_UniversityUserId_RoleAutoIncrementId");

            migrationBuilder.RenameIndex(
                name: "IX_UserApdysRoleMappings_ApdysRoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                newName: "IX_UserApdysRoleMappings_RoleAutoIncrementId");

            migrationBuilder.RenameColumn(
                name: "ApdysRoleAutoIncrementId",
                table: "ApdysRolePermissions",
                newName: "RoleAutoIncrementId");

            migrationBuilder.RenameColumn(
                name: "ApdysPermissionAutoIncrementId",
                table: "ApdysRolePermissions",
                newName: "PermissionAutoIncrementId");

            migrationBuilder.RenameIndex(
                name: "IX_ApdysRolePermissions_ApdysRoleAutoIncrementId_ApdysPermissi~",
                table: "ApdysRolePermissions",
                newName: "IX_ApdysRolePermissions_RoleAutoIncrementId_PermissionAutoIncr~");

            migrationBuilder.RenameIndex(
                name: "IX_ApdysRolePermissions_ApdysPermissionAutoIncrementId",
                table: "ApdysRolePermissions",
                newName: "IX_ApdysRolePermissions_PermissionAutoIncrementId");

            migrationBuilder.AlterColumn<string>(
                name: "PermissionName",
                table: "ApdysPermissions",
                type: "character varying(100)",
                maxLength: 100,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(150)",
                oldMaxLength: 150);

            migrationBuilder.AddForeignKey(
                name: "FK_ApdysRolePermissions_ApdysPermissions_PermissionAutoIncreme~",
                table: "ApdysRolePermissions",
                column: "PermissionAutoIncrementId",
                principalTable: "ApdysPermissions",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ApdysRolePermissions_ApdysRoles_RoleAutoIncrementId",
                table: "ApdysRolePermissions",
                column: "RoleAutoIncrementId",
                principalTable: "ApdysRoles",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserApdysRoleMappings_ApdysRoles_RoleAutoIncrementId",
                table: "UserApdysRoleMappings",
                column: "RoleAutoIncrementId",
                principalTable: "ApdysRoles",
                principalColumn: "AutoIncrementId",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
