using Rlx.Shared.Models;
using System.ComponentModel.DataAnnotations;

namespace AcademicPerformance.Models.AcademicPerformanceDbContextModels;

public class StaffCompetencyEvaluationEntity : EntityBaseModel
{
    [Required]
    public required string AcademicianUniveristyUserId { get; set; }

    [Required]
    public required string EvaluatingManagerUserId { get; set; }

    [Required]
    [StringLength(100)]
    public required string EvaluationContextId { get; set; }

    [StringLength(4000)]
    public string? OverallComments { get; set; }

    public DateTime SubmittedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<CompetencyRatingEntity>? CompetencyRatings { get; set; } = new List<CompetencyRatingEntity>();
}
