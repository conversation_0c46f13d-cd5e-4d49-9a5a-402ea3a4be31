using Microsoft.EntityFrameworkCore;
using AcademicPerformance.Models.AcademicPerformanceDbContextModels;
using Rlx.Shared.Interfaces;
using Rlx.Shared.Models.RlxEnumDbContextModels;
using Rlx.Shared.Models.RlxLocalizationDbContextModels;
namespace AcademicPerformance.DbContexts;

public class AcademicPerformanceDbContext : DbContext
{
    private readonly IEntityChangeLogHelper _entityChangeLogHelper;
    private readonly IConfiguration _configuration;
    public AcademicPerformanceDbContext(DbContextOptions<AcademicPerformanceDbContext> options,
        IEntityChangeLogHelper entityChangeLogHelper,
        IConfiguration configuration) : base(options)
    {
        _entityChangeLogHelper = entityChangeLogHelper;
        _configuration = configuration;
    }
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        ConfigureRbacEntities(builder);
        ConfigureFormManagementEntities(builder);
        ConfigureSubmissionEntities(builder);
        ConfigureWorkflowEntities(builder);
        ConfigureReportingEntities(builder);
        ConfigureRlxEnumEntities(builder);
    }
    public DbSet<ApdysRoleEntity> ApdysRoles { get; set; } = null!;
    public DbSet<UserApdysRoleMappingEntity> UserApdysRoleMappings { get; set; } = null!;
    public DbSet<AcademicianProfileEntity> AcademicianProfiles { get; set; } = null!;
    public DbSet<EvaluationFormEntity> EvaluationForms { get; set; } = null!;
    public DbSet<FormCategoryEntity> FormCategories { get; set; } = null!;
    public DbSet<FormCriterionLinkEntity> FormCriterionLinks { get; set; } = null!;
    public DbSet<StaticCriterionDefinitionEntity> StaticCriterionDefinitions { get; set; } = null!;
    public DbSet<StaticCriterionCoefficientEntity> StaticCriterionCoefficients { get; set; } = null!;
    public DbSet<AcademicSubmissionEntity> AcademicSubmissions { get; set; } = null!;
    public DbSet<EvidenceFileEntity> EvidenceFiles { get; set; } = null!;
    public DbSet<SubmissionAuditEntity> SubmissionAudits { get; set; } = null!;
    public DbSet<DepartmentStrategicPerformanceDataEntity> DepartmentStrategicPerformanceData { get; set; } = null!;
    public DbSet<DepartmentStrategicIndicatorDefinitionEntity> DepartmentStrategicIndicatorDefinitions { get; set; } = null!;
    public DbSet<DepartmentPerformanceEntity> DepartmentPerformances { get; set; } = null!;
    public DbSet<StaffCompetencyEvaluationEntity> StaffCompetencyEvaluations { get; set; } = null!;
    public DbSet<StaffCompetencyDefinitionEntity> StaffCompetencyDefinitions { get; set; } = null!;
    public DbSet<CompetencyRatingEntity> CompetencyRatings { get; set; } = null!;
    public DbSet<PortfolioVerificationLogEntity> PortfolioVerificationLogs { get; set; } = null!;
    public DbSet<PortfolioChecklistItemDefinitionEntity> PortfolioChecklistItemDefinitions { get; set; } = null!;
    public DbSet<CoursePortfolioVerificationEntity> CoursePortfolioVerifications { get; set; } = null!;
    public DbSet<GenericDataEntryRecordEntity> GenericDataEntryRecords { get; set; } = null!;
    public DbSet<GenericDataEntryDefinitionEntity> GenericDataEntryDefinitions { get; set; } = null!;
    public DbSet<RlxLocalization> RlxLocalizations { get; set; } = null!;
    public DbSet<RlxEnum> RlxEnums { get; set; } = null!;
    public DbSet<SubmissionFeedbackEntity> SubmissionFeedbacks { get; set; } = null!;
    public DbSet<CriterionFeedbackEntity> CriterionFeedbacks { get; set; } = null!;
    public DbSet<FeedbackNotificationLogEntity> FeedbackNotificationLogs { get; set; } = null!;
    public DbSet<FeedbackEntryEntity> FeedbackEntries { get; set; } = null!;
    public DbSet<PerformanceDistributionEntity> PerformanceDistributions { get; set; } = null!;
    public DbSet<TrendDataPointEntity> TrendDataPoints { get; set; } = null!;
    public DbSet<RlxEnumValue> RlxEnumValues { get; set; } = null!;
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        if (_configuration["EntityLog:Enabled"] == "1")
        {
            await _entityChangeLogHelper.AddEntityChangeLogAsync(dbContext: this);
        }
        return await base.SaveChangesAsync(cancellationToken);
    }
    private void ConfigureRbacEntities(ModelBuilder builder)
    {
        builder.Entity<ApdysRoleEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.RoleName).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(500);
            e.Property(e2 => e2.CreatedAt).IsRequired();
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.RoleName).IsUnique();
        });
        builder.Entity<UserApdysRoleMappingEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.UniversityUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.AssignedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.RevokedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => new { e2.UniversityUserId, e2.ApdysRoleAutoIncrementId }).IsUnique();
            e.HasIndex(e2 => e2.AssignedAt);
            e.HasOne(e2 => e2.Role)
                .WithMany(r => r.UserMappings)
                .HasForeignKey(e2 => e2.ApdysRoleAutoIncrementId)
                .HasPrincipalKey(r => r.AutoIncrementId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        builder.Entity<AcademicianProfileEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.UniversityUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.Name).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Surname).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.FullName).HasMaxLength(200);
            e.Property(e2 => e2.Department).HasMaxLength(200);
            e.Property(e2 => e2.DepartmentId).HasMaxLength(100);
            e.Property(e2 => e2.FacultyId).HasMaxLength(100);
            e.Property(e2 => e2.AcademicCadre).HasMaxLength(100);
            e.Property(e2 => e2.Email).HasMaxLength(200);
            e.Property(e2 => e2.Title).HasMaxLength(100);
            e.Property(e2 => e2.SyncNotes).HasMaxLength(500);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.UniversityUserId).IsUnique();
            e.HasIndex(e2 => e2.AcademicCadre);
            e.HasIndex(e2 => e2.Department);
            e.HasIndex(e2 => e2.DepartmentId);
            e.HasIndex(e2 => e2.FacultyId);
            e.HasIndex(e2 => e2.LastSyncedAt);
            e.HasIndex(e2 => e2.IsActive);
            e.HasIndex(e2 => new { e2.DepartmentId, e2.AcademicCadre });
            e.HasIndex(e2 => new { e2.FacultyId, e2.DepartmentId });
            e.HasIndex(e2 => new { e2.IsActive, e2.AcademicCadre });
        });
    }
    private void ConfigureFormManagementEntities(ModelBuilder builder)
    {
        builder.Entity<EvaluationFormEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(200).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.ApplicableAcademicCadresJson).HasMaxLength(1000);
            e.Property(e2 => e2.Status).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.EvaluationPeriodStartDate);
            e.HasIndex(e2 => e2.EvaluationPeriodEndDate);
            e.HasIndex(e2 => e2.SubmissionDeadline);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => new { e2.Status, e2.EvaluationPeriodStartDate });
            e.HasIndex(e2 => new { e2.Status, e2.SubmissionDeadline });
            e.HasIndex(e2 => new { e2.EvaluationPeriodStartDate, e2.EvaluationPeriodEndDate });
            e.ToTable(t => t.HasCheckConstraint("CK_EvaluationForm_Status", "\"Status\" IN ('Draft', 'Active', 'Archived')"));
        });
        builder.Entity<FormCategoryEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(200).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.Weight).HasPrecision(5, 4); // For values like 0.4 (40%)
            e.Property(e2 => e2.CreatedAt).IsRequired();
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedAt).IsRequired();
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.EvaluationFormAutoIncrementId);
            e.HasIndex(e2 => new { e2.EvaluationFormAutoIncrementId, e2.DisplayOrder });
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => e2.UpdatedAt);
            e.HasIndex(e2 => new { e2.EvaluationFormAutoIncrementId, e2.Weight });
            e.HasOne(e2 => e2.EvaluationForm)
                .WithMany(f => f.Categories)
                .HasForeignKey(e2 => e2.EvaluationFormAutoIncrementId)
                .HasPrincipalKey(f => f.AutoIncrementId)
                .OnDelete(DeleteBehavior.Cascade);
        });
        builder.Entity<FormCriterionLinkEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.StaticCriterionSystemId).HasMaxLength(100);
            e.Property(e2 => e2.DynamicCriterionTemplateId).HasMaxLength(100);
            e.Property(e2 => e2.CriterionType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.WeightPercentage).HasPrecision(5, 2);
            e.Property(e2 => e2.CreatedAt).IsRequired();
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.FormCategoryAutoIncrementId);
            e.HasIndex(e2 => new { e2.FormCategoryAutoIncrementId, e2.DisplayOrder });
            e.ToTable(t => t.HasCheckConstraint("CK_FormCriterionLink_CriterionType", "\"CriterionType\" IN ('Static', 'Dynamic')"));
            e.HasOne(e2 => e2.FormCategory)
                .WithMany(c => c.CriterionLinks)
                .HasForeignKey(e2 => e2.FormCategoryAutoIncrementId)
                .HasPrincipalKey(c => c.AutoIncrementId)
                .OnDelete(DeleteBehavior.Cascade);
            e.HasOne(e2 => e2.StaticCriterionDefinition)
                .WithMany(s => s.FormCriterionLinks)
                .HasForeignKey(e2 => e2.StaticCriterionSystemId)
                .HasPrincipalKey(s => s.StaticCriterionSystemId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        builder.Entity<StaticCriterionDefinitionEntity>(e =>
        {
            e.HasKey(e2 => e2.StaticCriterionSystemId);
            e.Property(e2 => e2.StaticCriterionSystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Name).HasMaxLength(200).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.DataType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.DataSourceHint).HasMaxLength(500);
            e.Property(e2 => e2.CalculationLogic).HasMaxLength(2000);
            e.HasIndex(e2 => e2.StaticCriterionSystemId).IsUnique();
            e.HasIndex(e2 => e2.IsActive);
            e.ToTable(t => t.HasCheckConstraint("CK_StaticCriterion_DataType", "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')"));
        });
        builder.Entity<StaticCriterionCoefficientEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.StaticCriterionSystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.CriterionName).HasMaxLength(200).IsRequired();
            e.Property(e2 => e2.Coefficient).HasPrecision(18, 4).IsRequired();
            e.Property(e2 => e2.MaximumLimit).HasPrecision(18, 4);
            e.Property(e2 => e2.MinimumLimit).HasPrecision(18, 4);
            e.Property(e2 => e2.Category).HasMaxLength(10);
            e.Property(e2 => e2.Description).HasMaxLength(500);
            e.Property(e2 => e2.DataSource).HasMaxLength(100);
            e.Property(e2 => e2.LastUpdatedBy).HasMaxLength(100);
            e.Property(e2 => e2.CreatedBy).HasMaxLength(100);
            e.Property(e2 => e2.CoefficientHistory).HasColumnType("text");
            e.Property(e2 => e2.ApprovedBy).HasMaxLength(100);
            e.Property(e2 => e2.RowVersion).IsRowVersion();
            e.HasIndex(e2 => e2.StaticCriterionSystemId);
            e.HasIndex(e2 => e2.IsActive);
            e.HasIndex(e2 => e2.Category);
            e.HasIndex(e2 => e2.DisplayOrder);
            e.HasIndex(e2 => e2.IsApproved);
            e.HasIndex(e2 => e2.EffectiveFrom);
            e.HasIndex(e2 => e2.EffectiveTo);
        });
    }
    private void ConfigureSubmissionEntities(ModelBuilder builder)
    {
        builder.Entity<AcademicSubmissionEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.Status).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.ApprovedByControllerUserId).HasMaxLength(256);
            e.Property(e2 => e2.RejectedByControllerUserId).HasMaxLength(256);
            e.Property(e2 => e2.ApprovalComments).HasMaxLength(2000);
            e.Property(e2 => e2.RejectionComments).HasMaxLength(2000);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
            e.HasIndex(e2 => e2.EvaluationFormAutoIncrementId);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.EvaluationFormAutoIncrementId }).IsUnique();
            e.HasIndex(e2 => e2.SubmittedAt);
            e.HasIndex(e2 => e2.ApprovedByControllerUserId);
            e.HasIndex(e2 => e2.RejectedByControllerUserId);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => e2.UpdatedAt);
            e.HasIndex(e2 => new { e2.Status, e2.SubmittedAt });
            e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.Status });
            e.HasIndex(e2 => new { e2.EvaluationFormAutoIncrementId, e2.Status });
            e.HasIndex(e2 => new { e2.CreatedAt, e2.Status });
            e.ToTable(t => t.HasCheckConstraint("CK_AcademicSubmission_Status",
                "\"Status\" IN ('Draft', 'Submitted', 'UnderReview', 'Approved', 'Rejected', 'RequiresRevision')"));
            e.HasOne(e2 => e2.EvaluationForm)
                .WithMany(f => f.Submissions)
                .HasForeignKey(e2 => e2.EvaluationFormAutoIncrementId)
                .HasPrincipalKey(f => f.AutoIncrementId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(e2 => e2.AcademicianProfile)
                .WithMany(ap => ap.Submissions)
                .HasForeignKey(e2 => e2.AcademicianUniveristyUserId)
                .HasPrincipalKey(ap => ap.UniversityUserId)
                .OnDelete(DeleteBehavior.Restrict);
            e.HasOne(e2 => e2.AcademicianProfile)
                .WithMany(ap => ap.Submissions)
                .HasForeignKey(e2 => e2.AcademicianUniveristyUserId)
                .HasPrincipalKey(ap => ap.UniversityUserId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        builder.Entity<EvidenceFileEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.FormCriterionLinkId).HasMaxLength(100);
            e.Property(e2 => e2.SubmittedDynamicDataInstanceId).HasMaxLength(100);
            e.Property(e2 => e2.FileName).HasMaxLength(500).IsRequired();
            e.Property(e2 => e2.StoredFilePath).HasMaxLength(1000).IsRequired();
            e.Property(e2 => e2.ContentType).HasMaxLength(200).IsRequired();
            e.Property(e2 => e2.UploadedByUniveristyUserId).HasMaxLength(256);
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            // MinIO specific field configurations
            e.Property(e2 => e2.MinioBucketName).HasMaxLength(100);
            e.Property(e2 => e2.MinioObjectName).HasMaxLength(500);
            e.Property(e2 => e2.MinioETag).HasMaxLength(100);
            e.Property(e2 => e2.StorageType).HasMaxLength(50).IsRequired().HasDefaultValue("Local");
            e.Property(e2 => e2.AccessUrl).HasMaxLength(2000);
            e.Property(e2 => e2.FileChecksum).HasMaxLength(100);
            e.Property(e2 => e2.OriginalFileName).HasMaxLength(500);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.AcademicSubmissionAutoIncrementId);
            e.HasIndex(e2 => e2.UploadedAt);
            e.HasIndex(e2 => e2.StorageType);
            e.HasIndex(e2 => e2.MinioBucketName);
            e.HasIndex(e2 => e2.MinioObjectName);
            e.HasIndex(e2 => e2.ContentType);
            e.HasIndex(e2 => e2.UploadedByUniveristyUserId);
            e.HasIndex(e2 => new { e2.AcademicSubmissionAutoIncrementId, e2.UploadedAt });
            e.HasIndex(e2 => new { e2.StorageType, e2.UploadedAt });
            e.HasIndex(e2 => new { e2.UploadedByUniveristyUserId, e2.UploadedAt });
            // Relationships
            e.HasOne(e2 => e2.AcademicSubmission)
                .WithMany(s => s.EvidenceFiles)
                .HasForeignKey(e2 => e2.AcademicSubmissionAutoIncrementId)
                .HasPrincipalKey(s => s.AutoIncrementId)
                .OnDelete(DeleteBehavior.Cascade);
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_EvidenceFile_Size", "SizeBytes > 0"));
            e.ToTable(t => t.HasCheckConstraint("CK_EvidenceFile_StorageType", "StorageType IN ('Local', 'MinIO')"));
        });
        //iz takip
        builder.Entity<SubmissionAuditEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Action).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.PerformedByUserId).HasMaxLength(255).IsRequired();
            e.Property(e2 => e2.PerformedByUserRole).HasMaxLength(100);
            e.Property(e2 => e2.PerformedByUserName).HasMaxLength(255);
            e.Property(e2 => e2.Comments).HasMaxLength(2000);
            e.Property(e2 => e2.OldValue).HasMaxLength(500);
            e.Property(e2 => e2.NewValue).HasMaxLength(500);
            e.Property(e2 => e2.EntityType).HasMaxLength(100);
            e.Property(e2 => e2.EntityId).HasMaxLength(255);
            e.Property(e2 => e2.Category).HasMaxLength(100);
            e.Property(e2 => e2.IpAddress).HasMaxLength(45);
            e.Property(e2 => e2.UserAgent).HasMaxLength(500);
            e.Property(e2 => e2.ErrorMessage).HasMaxLength(1000);
            e.Property(e2 => e2.SessionId).HasMaxLength(255);
            e.Property(e2 => e2.RequestId).HasMaxLength(255);
            e.Property(e2 => e2.Metadata).HasColumnType("text");
            // Indexes for performance
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.AcademicSubmissionAutoIncrementId);
            e.HasIndex(e2 => e2.Action);
            e.HasIndex(e2 => e2.PerformedByUserId);
            e.HasIndex(e2 => e2.PerformedAt);
            e.HasIndex(e2 => e2.Category);
            e.HasIndex(e2 => e2.EntityType);
            e.HasIndex(e2 => e2.IsSuccessful);
            e.HasIndex(e2 => new { e2.AcademicSubmissionAutoIncrementId, e2.PerformedAt });
            e.HasIndex(e2 => new { e2.Action, e2.PerformedAt });
            e.HasIndex(e2 => new { e2.PerformedByUserId, e2.PerformedAt });
            e.HasIndex(e2 => new { e2.Category, e2.PerformedAt });
            e.HasIndex(e2 => new { e2.IsSuccessful, e2.PerformedAt });
            e.HasIndex(e2 => new { e2.EntityType, e2.EntityId, e2.PerformedAt });
            // Relationship with AcademicSubmission
            e.HasOne(e2 => e2.AcademicSubmission)
                .WithMany()
                .HasForeignKey(e2 => e2.AcademicSubmissionAutoIncrementId)
                .HasPrincipalKey(s => s.AutoIncrementId)
                .OnDelete(DeleteBehavior.Cascade);
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_SubmissionAudit_DurationMs", "DurationMs >= 0"));
        });
    }
    private void ConfigureWorkflowEntities(ModelBuilder builder)
    {
        builder.Entity<DepartmentStrategicIndicatorDefinitionEntity>(e =>
        {
            e.HasKey(e2 => e2.IndicatorSystemId);
            e.Property(e2 => e2.IndicatorSystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.DataType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.IndicatorSystemId).IsUnique();
            e.HasIndex(e2 => e2.IsActive);
            e.ToTable(t => t.HasCheckConstraint("CK_DepartmentStrategicIndicator_DataType",
                "\"DataType\" IN ('Integer', 'Decimal', 'Boolean', 'String', 'Date')"));
        });
        builder.Entity<DepartmentStrategicPerformanceDataEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.DepartmentId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.AssessmentPeriodId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.IndicatorSystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.ActualValue).HasMaxLength(2000);
            e.Property(e2 => e2.TargetValue).HasMaxLength(2000);
            e.Property(e2 => e2.Notes).HasMaxLength(2000);
            e.Property(e2 => e2.SubmittedByUserId).HasMaxLength(256).IsRequired();
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.DepartmentId);
            e.HasIndex(e2 => e2.AssessmentPeriodId);
            e.HasIndex(e2 => new { e2.DepartmentId, e2.AssessmentPeriodId, e2.IndicatorSystemId }).IsUnique();
            e.HasIndex(e2 => e2.SubmittedAt);
            e.HasOne(e2 => e2.IndicatorDefinition)
                .WithMany(i => i.PerformanceData)
                .HasForeignKey(e2 => e2.IndicatorSystemId)
                .HasPrincipalKey(i => i.IndicatorSystemId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        // Staff Competency Definition
        builder.Entity<StaffCompetencyDefinitionEntity>(e =>
        {
            e.HasKey(e2 => e2.CompetencySystemId);
            e.Property(e2 => e2.CompetencySystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.RatingScaleJson).HasMaxLength(4000);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.CompetencySystemId).IsUnique();
            e.HasIndex(e2 => e2.IsActive);
        });
        builder.Entity<StaffCompetencyEvaluationEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.EvaluatingManagerUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.EvaluationContextId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.OverallComments).HasMaxLength(4000);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
            e.HasIndex(e2 => e2.EvaluationContextId);
            e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.EvaluationContextId }).IsUnique();
            e.HasIndex(e2 => e2.SubmittedAt);
        });
        builder.Entity<CompetencyRatingEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.CompetencySystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Rating).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Comments).HasMaxLength(1000);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.StaffCompetencyEvaluationAutoIncrementId);
            e.HasIndex(e2 => new { e2.StaffCompetencyEvaluationAutoIncrementId, e2.CompetencySystemId }).IsUnique();
            e.HasOne(e2 => e2.Evaluation)
                .WithMany(s => s.CompetencyRatings)
                .HasForeignKey(e2 => e2.StaffCompetencyEvaluationAutoIncrementId)
                .HasPrincipalKey(s => s.AutoIncrementId)
                .OnDelete(DeleteBehavior.Cascade);
            e.HasOne(e2 => e2.CompetencyDefinition)
                .WithMany(c => c.CompetencyRatings)
                .HasForeignKey(e2 => e2.CompetencySystemId)
                .HasPrincipalKey(c => c.CompetencySystemId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        builder.Entity<PortfolioVerificationLogEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.AcademicianUniveristyUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.ItemSystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.VerificationStatus).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.VerificationNotes).HasMaxLength(2000);
            e.Property(e2 => e2.LastVerifiedByArchivistUserId).HasMaxLength(256);
            e.Property(e2 => e2.EbysReferenceId).HasMaxLength(200);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.AcademicianUniveristyUserId);
            e.HasIndex(e2 => e2.ItemSystemId);
            e.HasIndex(e2 => new { e2.AcademicianUniveristyUserId, e2.ItemSystemId }).IsUnique();
            e.HasIndex(e2 => e2.VerificationStatus);
            e.HasIndex(e2 => e2.LastVerifiedByArchivistUserId);
            e.ToTable(t => t.HasCheckConstraint("CK_PortfolioVerification_Status",
                "\"VerificationStatus\" IN ('VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.HasOne(e2 => e2.ChecklistItemDefinition)
                .WithMany(ci => ci.VerificationLogs)
                .HasForeignKey(e2 => e2.ItemSystemId)
                .HasPrincipalKey(ci => ci.ItemSystemId)
                .OnDelete(DeleteBehavior.Restrict);
        });
        // Course Portfolio Verification Entity Configuration
        builder.Entity<CoursePortfolioVerificationEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.AcademicianTc).HasMaxLength(11).IsRequired();
            e.Property(e2 => e2.CourseCode).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.PeriodName).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.CourseName).HasMaxLength(200).IsRequired();
            // Portfolio status fields
            e.Property(e2 => e2.ExamPapersStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.AnswerKeyStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.ExamRecordStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.AttendanceSheetStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.CourseSyllabusStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.WeeklyAttendanceStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.MakeupExamGradesStatus).HasMaxLength(50).HasDefaultValue("Pending");
            e.Property(e2 => e2.UzemRecordsStatus).HasMaxLength(50).HasDefaultValue("Pending");
            // Other fields
            e.Property(e2 => e2.VerificationNotes).HasMaxLength(2000);
            e.Property(e2 => e2.LastVerifiedByArchivistId).HasMaxLength(256);
            e.Property(e2 => e2.EbysReferenceId).HasMaxLength(200);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.AcademicianTc);
            e.HasIndex(e2 => e2.PeriodName);
            e.HasIndex(e2 => e2.CourseCode);
            e.HasIndex(e2 => new { e2.AcademicianTc, e2.CourseCode, e2.PeriodName }).IsUnique();
            e.HasIndex(e2 => e2.LastVerifiedAt);
            e.HasIndex(e2 => e2.LastVerifiedByArchivistId);
            // Check constraints for status values
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_ExamPapersStatus",
                "\"ExamPapersStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_AnswerKeyStatus",
                "\"AnswerKeyStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_ExamRecordStatus",
                "\"ExamRecordStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_AttendanceSheetStatus",
                "\"AttendanceSheetStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_CourseSyllabusStatus",
                "\"CourseSyllabusStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_WeeklyAttendanceStatus",
                "\"WeeklyAttendanceStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_MakeupExamGradesStatus",
                "\"MakeupExamGradesStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CoursePortfolioVerification_UzemRecordsStatus",
                "\"UzemRecordsStatus\" IN ('Pending', 'VerifiedInEbys', 'Missing', 'DiscrepancyFound', 'NotApplicable')"));
        });
        // Portfolio Checklist Item Definition
        builder.Entity<PortfolioChecklistItemDefinitionEntity>(e =>
        {
            e.HasKey(e2 => e2.ItemSystemId);
            e.Property(e2 => e2.ItemSystemId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.Category).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.EbysDataSourceHint).HasMaxLength(500);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.ItemSystemId).IsUnique();
            e.HasIndex(e2 => e2.Category);
            e.HasIndex(e2 => e2.IsActive);
        });
        // Generic Data Entry Definition
        builder.Entity<GenericDataEntryDefinitionEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(250).IsRequired();
            e.Property(e2 => e2.Description).HasMaxLength(1000);
            e.Property(e2 => e2.Category).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.FieldDefinitions).HasMaxLength(500);
            e.Property(e2 => e2.ValidationRules).HasMaxLength(500);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.Name);
            e.HasIndex(e2 => e2.Category);
            e.HasIndex(e2 => e2.IsActive);
        });
        builder.Entity<GenericDataEntryRecordEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.DefinitionId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.RecordData).IsRequired();
            e.Property(e2 => e2.Status).HasMaxLength(50);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.DefinitionId);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasOne(e2 => e2.Definition)
                .WithMany(d => d.DataEntryRecords)
                .HasForeignKey(e2 => e2.DefinitionId)
                .OnDelete(DeleteBehavior.Restrict);
        });
    }
    private void ConfigureRlxEnumEntities(ModelBuilder builder)
    {
        builder.Entity<RlxEnum>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(256);
            e.Property(e2 => e2.Code).HasMaxLength(256);
        });
        builder.Entity<RlxEnum>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxEnumValue>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.Name).HasMaxLength(256);
            e.Property(e2 => e2.Code).HasMaxLength(256);
        });
        builder.Entity<RlxEnumValue>().HasIndex(u => u.AutoIncrementId);
        builder.Entity<RlxEnum>(e =>
        {
            e.HasMany(e2 => e2.RlxEnumValues)
            .WithOne(e2 => e2.RlxEnum)
            .HasForeignKey(e2 => e2.EnumId)
            .HasPrincipalKey(e2 => e2.AutoIncrementId);
        });
        // RlxLocalization configuration moved to view-only for main DbContext
        builder.Entity<RlxLocalization>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).HasColumnType("integer");
            e.Property(e2 => e2.Culture).IsRequired().HasColumnType("text");
            e.Property(e2 => e2.Key).IsRequired().HasColumnType("text");
            e.Property(e2 => e2.ReferenceId).IsRequired().HasColumnType("text");
            e.Property(e2 => e2.Value).IsRequired().HasColumnType("text");
            e.HasKey(e2 => e2.Id);
            e.ToTable((string)null);
            e.ToView("RlxLocalizations", (string)null);
        });
    }
    private void ConfigureReportingEntities(ModelBuilder builder)
    {
        // FeedbackEntry Entity Configuration
        builder.Entity<FeedbackEntryEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.SubmissionId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.FeedbackType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.CreatedBy).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.Comments).HasMaxLength(2000);
            e.Property(e2 => e2.Status).HasMaxLength(50).IsRequired().HasDefaultValue("Active");
            e.Property(e2 => e2.Priority).HasMaxLength(20).HasDefaultValue("Medium");
            e.Property(e2 => e2.Category).HasMaxLength(50);
            e.Property(e2 => e2.ResolutionNote).HasMaxLength(1000);
            e.Property(e2 => e2.FormId).HasMaxLength(100);
            e.Property(e2 => e2.CriterionId).HasMaxLength(100);
            e.Property(e2 => e2.AcademicianUserId).HasMaxLength(256);
            e.Property(e2 => e2.DepartmentId).HasMaxLength(100);
            e.Property(e2 => e2.Metadata).HasColumnType("text");
            e.Property(e2 => e2.UpdatedBy).HasMaxLength(256);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.SubmissionId);
            e.HasIndex(e2 => e2.FeedbackType);
            e.HasIndex(e2 => e2.CreatedBy);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.AcademicianUserId);
            e.HasIndex(e2 => e2.DepartmentId);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => new { e2.SubmissionId, e2.FeedbackType });
            e.HasIndex(e2 => new { e2.AcademicianUserId, e2.CreatedAt });
            e.HasIndex(e2 => new { e2.DepartmentId, e2.CreatedAt });
            e.HasIndex(e2 => new { e2.Status, e2.Priority });
            e.HasIndex(e2 => new { e2.FeedbackType, e2.Status });
            e.HasIndex(e2 => new { e2.CreatedAt, e2.Status });
            e.HasIndex(e2 => new { e2.AcademicianUserId, e2.FeedbackType, e2.Status });
            // Check constraints using modern syntax
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackEntry_FeedbackType",
                "\"FeedbackType\" IN ('Approval', 'Rejection', 'RevisionRequest', 'Comment')"));
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackEntry_Status",
                "\"Status\" IN ('Active', 'Resolved', 'Superseded')"));
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackEntry_Priority",
                "\"Priority\" IN ('Low', 'Medium', 'High', 'Critical')"));
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackEntry_Rating",
                "\"Rating\" IS NULL OR (\"Rating\" >= 1 AND \"Rating\" <= 10)"));
            // Relationships
            e.HasOne(e2 => e2.AcademicSubmission)
                .WithMany()
                .HasForeignKey(e2 => e2.SubmissionId)
                .HasPrincipalKey(s => s.Id)
                .OnDelete(DeleteBehavior.Cascade);
            e.HasOne(e2 => e2.EvaluationForm)
                .WithMany()
                .HasForeignKey(e2 => e2.FormId)
                .HasPrincipalKey(f => f.Id)
                .OnDelete(DeleteBehavior.SetNull);
        });
        // CriterionFeedback Entity Configuration
        builder.Entity<CriterionFeedbackEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.SubmissionFeedbackId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.CriterionLinkId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.FeedbackMessage).HasMaxLength(1000).IsRequired();
            e.Property(e2 => e2.Status).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.RevisionType).HasMaxLength(50);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.SubmissionFeedbackId);
            e.HasIndex(e2 => e2.CriterionLinkId);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => e2.IsActive);
            e.HasIndex(e2 => e2.IsRequired);
            e.HasIndex(e2 => new { e2.SubmissionFeedbackId, e2.CriterionLinkId });
            e.HasIndex(e2 => new { e2.Status, e2.IsRequired });
            e.HasIndex(e2 => new { e2.CriterionLinkId, e2.Status });
            e.HasIndex(e2 => new { e2.CreatedAt, e2.Status });
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_CriterionFeedback_Status",
                "\"Status\" IN ('Approved', 'NeedsRevision', 'Rejected', 'Excellent', 'Good', 'Satisfactory', 'NeedsImprovement')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CriterionFeedback_RevisionType",
                "\"RevisionType\" IS NULL OR \"RevisionType\" IN ('DataCorrection', 'EvidenceUpdate', 'AdditionalInfo')"));
            e.ToTable(t => t.HasCheckConstraint("CK_CriterionFeedback_Rating",
                "\"Rating\" IS NULL OR (\"Rating\" >= 1 AND \"Rating\" <= 10)"));
            // Relationships
            e.HasOne(e2 => e2.SubmissionFeedback)
                .WithMany(sf => sf.CriterionFeedbacks)
                .HasForeignKey(e2 => e2.SubmissionFeedbackId)
                .HasPrincipalKey(sf => sf.Id)
                .OnDelete(DeleteBehavior.Cascade);
        });
        // SubmissionFeedback Entity Configuration
        builder.Entity<SubmissionFeedbackEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.SubmissionId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.FeedbackType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.ControllerUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.Message).HasMaxLength(2000).IsRequired();
            e.Property(e2 => e2.Priority).HasMaxLength(20).HasDefaultValue("Medium");
            e.Property(e2 => e2.AcademicianResponse).HasMaxLength(2000);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.SubmissionId);
            e.HasIndex(e2 => e2.FeedbackType);
            e.HasIndex(e2 => e2.ControllerUserId);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => e2.IsActive);
            e.HasIndex(e2 => e2.NotificationSent);
            e.HasIndex(e2 => new { e2.SubmissionId, e2.FeedbackType });
            e.HasIndex(e2 => new { e2.ControllerUserId, e2.CreatedAt });
            e.HasIndex(e2 => new { e2.FeedbackType, e2.IsActive });
            e.HasIndex(e2 => new { e2.CreatedAt, e2.IsActive });
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_SubmissionFeedback_FeedbackType",
                "\"FeedbackType\" IN ('Approval', 'Rejection', 'RevisionRequest')"));
            e.ToTable(t => t.HasCheckConstraint("CK_SubmissionFeedback_Priority",
                "\"Priority\" IN ('Low', 'Medium', 'High')"));
            // Relationships
            e.HasOne(e2 => e2.Submission)
                .WithMany()
                .HasForeignKey(e2 => e2.SubmissionId)
                .HasPrincipalKey(s => s.Id)
                .OnDelete(DeleteBehavior.Cascade);
        });
        // FeedbackNotificationLog Entity Configuration
        builder.Entity<FeedbackNotificationLogEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.SubmissionFeedbackId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.NotificationType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.RecipientUserId).HasMaxLength(256).IsRequired();
            e.Property(e2 => e2.Subject).HasMaxLength(200).IsRequired();
            e.Property(e2 => e2.Content).HasMaxLength(2000).IsRequired();
            e.Property(e2 => e2.Status).HasMaxLength(20).IsRequired().HasDefaultValue("Pending");
            e.Property(e2 => e2.ErrorMessage).HasMaxLength(1000);
            e.Property(e2 => e2.CreatedByUserId).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedByUserId).HasMaxLength(256);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.SubmissionFeedbackId);
            e.HasIndex(e2 => e2.RecipientUserId);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.NotificationType);
            e.HasIndex(e2 => e2.CreatedAt);
            e.HasIndex(e2 => e2.SentAt);
            e.HasIndex(e2 => e2.RetryCount);
            e.HasIndex(e2 => new { e2.Status, e2.RetryCount });
            e.HasIndex(e2 => new { e2.RecipientUserId, e2.CreatedAt });
            e.HasIndex(e2 => new { e2.NotificationType, e2.Status });
            e.HasIndex(e2 => new { e2.SubmissionFeedbackId, e2.NotificationType });
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackNotificationLog_NotificationType",
                "\"NotificationType\" IN ('Email', 'SMS', 'InApp')"));
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackNotificationLog_Status",
                "\"Status\" IN ('Pending', 'Sent', 'Failed')"));
            e.ToTable(t => t.HasCheckConstraint("CK_FeedbackNotificationLog_RetryCount",
                "\"RetryCount\" >= 0"));
            // Relationships
            e.HasOne(e2 => e2.SubmissionFeedback)
                .WithMany()
                .HasForeignKey(e2 => e2.SubmissionFeedbackId)
                .HasPrincipalKey(sf => sf.Id)
                .OnDelete(DeleteBehavior.Cascade);
        });
        // PerformanceDistribution Entity Configuration
        builder.Entity<PerformanceDistributionEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.DistributionType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.EntityId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.EntityName).HasMaxLength(200);
            e.Property(e2 => e2.CalculationMethod).HasMaxLength(50).HasDefaultValue("Weighted");
            e.Property(e2 => e2.CalculationParameters).HasColumnType("text");
            e.Property(e2 => e2.Status).HasMaxLength(20).HasDefaultValue("Active");
            e.Property(e2 => e2.CalculatedBy).HasMaxLength(256);
            e.Property(e2 => e2.LastUpdatedBy).HasMaxLength(256);
            e.Property(e2 => e2.Metadata).HasColumnType("text");
            e.Property(e2 => e2.CreatedBy).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedBy).HasMaxLength(256);
            // Precision for decimal fields
            e.Property(e2 => e2.ExcellentPercentage).HasPrecision(5, 2);
            e.Property(e2 => e2.GoodPercentage).HasPrecision(5, 2);
            e.Property(e2 => e2.AveragePercentage).HasPrecision(5, 2);
            e.Property(e2 => e2.PoorPercentage).HasPrecision(5, 2);
            e.Property(e2 => e2.AverageScore).HasPrecision(8, 4);
            e.Property(e2 => e2.MedianScore).HasPrecision(8, 4);
            e.Property(e2 => e2.StandardDeviation).HasPrecision(8, 4);
            e.Property(e2 => e2.MinScore).HasPrecision(8, 4);
            e.Property(e2 => e2.MaxScore).HasPrecision(8, 4);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.DistributionType);
            e.HasIndex(e2 => e2.EntityId);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.CalculatedAt);
            e.HasIndex(e2 => new { e2.DistributionType, e2.EntityId });
            e.HasIndex(e2 => new { e2.EntityId, e2.PeriodStart, e2.PeriodEnd });
            e.HasIndex(e2 => new { e2.DistributionType, e2.CalculatedAt });
            e.HasIndex(e2 => new { e2.Status, e2.CalculatedAt });
            e.HasIndex(e2 => new { e2.DistributionType, e2.Status, e2.CalculatedAt });
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_PerformanceDistribution_DistributionType",
                "\"DistributionType\" IN ('Department', 'Faculty', 'University', 'Form', 'Category')"));
            e.ToTable(t => t.HasCheckConstraint("CK_PerformanceDistribution_Status",
                "\"Status\" IN ('Active', 'Archived', 'Draft')"));
            e.ToTable(t => t.HasCheckConstraint("CK_PerformanceDistribution_Counts",
                "\"ExcellentCount\" >= 0 AND \"GoodCount\" >= 0 AND \"AverageCount\" >= 0 AND \"PoorCount\" >= 0"));
            e.ToTable(t => t.HasCheckConstraint("CK_PerformanceDistribution_Percentages",
                "\"ExcellentPercentage\" >= 0 AND \"ExcellentPercentage\" <= 100"));
        });
        // TrendDataPoint Entity Configuration
        builder.Entity<TrendDataPointEntity>(e =>
        {
            e.HasKey(e2 => e2.Id);
            e.Property(e2 => e2.AutoIncrementId).ValueGeneratedOnAdd();
            e.Property(e2 => e2.TrendType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.EntityType).HasMaxLength(50).IsRequired();
            e.Property(e2 => e2.EntityId).HasMaxLength(100).IsRequired();
            e.Property(e2 => e2.EntityName).HasMaxLength(200);
            e.Property(e2 => e2.PeriodType).HasMaxLength(20).HasDefaultValue("Monthly");
            e.Property(e2 => e2.TrendDirection).HasMaxLength(20).HasDefaultValue("Stable");
            e.Property(e2 => e2.AnomalyType).HasMaxLength(50);
            e.Property(e2 => e2.DataSource).HasMaxLength(50).HasDefaultValue("Calculated");
            e.Property(e2 => e2.CalculationMethod).HasMaxLength(100);
            e.Property(e2 => e2.Status).HasMaxLength(20).HasDefaultValue("Active");
            e.Property(e2 => e2.AdditionalMetrics).HasColumnType("text");
            e.Property(e2 => e2.Metadata).HasColumnType("text");
            e.Property(e2 => e2.CreatedBy).HasMaxLength(256);
            e.Property(e2 => e2.UpdatedBy).HasMaxLength(256);
            // Precision for decimal fields
            e.Property(e2 => e2.Value).HasPrecision(18, 6);
            e.Property(e2 => e2.MovingAverage).HasPrecision(18, 6);
            e.Property(e2 => e2.TrendStrength).HasPrecision(8, 4);
            e.Property(e2 => e2.ChangeRate).HasPrecision(8, 4);
            e.Property(e2 => e2.ChangeAmount).HasPrecision(18, 6);
            e.Property(e2 => e2.PreviousValue).HasPrecision(18, 6);
            e.Property(e2 => e2.DataQualityScore).HasPrecision(5, 2);
            e.Property(e2 => e2.ConfidenceLowerBound).HasPrecision(18, 6);
            e.Property(e2 => e2.ConfidenceUpperBound).HasPrecision(18, 6);
            e.Property(e2 => e2.ConfidenceLevel).HasPrecision(4, 3);
            e.Property(e2 => e2.AnomalyScore).HasPrecision(5, 2);
            e.Property(e2 => e2.SeasonalityFactor).HasPrecision(8, 4);
            // Indexes
            e.HasIndex(e2 => e2.AutoIncrementId).IsUnique();
            e.HasIndex(e2 => e2.TrendType);
            e.HasIndex(e2 => e2.EntityType);
            e.HasIndex(e2 => e2.EntityId);
            e.HasIndex(e2 => e2.DataDate);
            e.HasIndex(e2 => e2.Status);
            e.HasIndex(e2 => e2.IsAnomaly);
            e.HasIndex(e2 => new { e2.TrendType, e2.EntityType, e2.EntityId });
            e.HasIndex(e2 => new { e2.EntityId, e2.DataDate });
            e.HasIndex(e2 => new { e2.TrendType, e2.DataDate });
            e.HasIndex(e2 => new { e2.EntityType, e2.DataDate });
            e.HasIndex(e2 => new { e2.PeriodType, e2.DataDate });
            e.HasIndex(e2 => new { e2.TrendDirection, e2.DataDate });
            e.HasIndex(e2 => new { e2.IsAnomaly, e2.DataDate });
            e.HasIndex(e2 => new { e2.Status, e2.DataDate });
            e.HasIndex(e2 => new { e2.TrendType, e2.EntityType, e2.DataDate });
            e.HasIndex(e2 => new { e2.EntityId, e2.PeriodStart, e2.PeriodEnd });
            // Check constraints
            e.ToTable(t => t.HasCheckConstraint("CK_TrendDataPoint_TrendType",
                "\"TrendType\" IN ('Performance', 'Completion', 'Quality', 'Engagement')"));
            e.ToTable(t => t.HasCheckConstraint("CK_TrendDataPoint_EntityType",
                "\"EntityType\" IN ('Academician', 'Department', 'Faculty', 'Form', 'Category')"));
            e.ToTable(t => t.HasCheckConstraint("CK_TrendDataPoint_PeriodType",
                "\"PeriodType\" IN ('Daily', 'Weekly', 'Monthly', 'Quarterly', 'Yearly')"));
            e.ToTable(t => t.HasCheckConstraint("CK_TrendDataPoint_TrendDirection",
                "\"TrendDirection\" IN ('Increasing', 'Decreasing', 'Stable')"));
            e.ToTable(t => t.HasCheckConstraint("CK_TrendDataPoint_Status",
                "\"Status\" IN ('Active', 'Archived', 'Draft', 'Validated')"));
            e.ToTable(t => t.HasCheckConstraint("CK_TrendDataPoint_DataSource",
                "\"DataSource\" IN ('Calculated', 'Manual', 'Imported', 'Predicted')"));
            // Relationships - Conditional based on EntityType
            // Note: These are optional relationships based on EntityType
            // EntityType = 'Academician' -> AcademicianProfile relationship
            // EntityType = 'Form' -> EvaluationForm relationship
            // Other EntityTypes may not have direct relationships
        });
    }
}
